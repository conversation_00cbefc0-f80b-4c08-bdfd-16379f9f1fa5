import mongoose, { Document, Schema } from 'mongoose';

export interface Conversation extends Document {
  _id: string;
  userId: string;
  title: string;
  messages?: any[];
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  metadata?: {
    platform?: string;
    appVersion?: string;
    userAgent?: string;
  };
}

const ConversationSchema = new Schema<Conversation>({
  userId: {
    type: String,
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true,
    default: 'New Chat'
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  updatedAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  metadata: {
    platform: String,
    appVersion: String,
    userAgent: String
  }
}, {
  timestamps: true,
  collection: 'ai_conversations'
});

// Indexes for better query performance
ConversationSchema.index({ userId: 1, updatedAt: -1 });
ConversationSchema.index({ userId: 1, isActive: 1 });

// Pre-save middleware to update the updatedAt field
ConversationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Instance methods
ConversationSchema.methods.markAsInactive = function() {
  this.isActive = false;
  this.updatedAt = new Date();
  return this.save();
};

ConversationSchema.methods.updateTitle = function(newTitle: string) {
  this.title = newTitle;
  this.updatedAt = new Date();
  return this.save();
};

// Static methods
ConversationSchema.statics.findActiveByUser = function(userId: string) {
  return this.find({ userId, isActive: true }).sort({ updatedAt: -1 });
};

ConversationSchema.statics.findRecentByUser = function(userId: string, limit: number = 10) {
  return this.find({ userId, isActive: true })
    .sort({ updatedAt: -1 })
    .limit(limit);
};

export const ConversationModel = mongoose.model<Conversation>('Conversation', ConversationSchema);
