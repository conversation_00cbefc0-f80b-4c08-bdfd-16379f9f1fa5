import { ScrollView } from 'react-native'
import {
  YStack,
  XStack,
  Text,
  Image,
  Button,
  Separator
} from 'tamagui'
import { Ionicons } from '@expo/vector-icons'
import { MotiView } from 'moti'
import { useCartStore } from './CartStore'
import { router } from 'expo-router'
import { useTranslation } from 'react-i18next'

export function CartPage({ category, onClose }: { category: string; onClose: () => void }) {
  const { t } = useTranslation();
  const itemsBySupplier = useCartStore(state => state.getItemsBySupplier());
  const removeItem = useCartStore(state => state.removeItem);
  const updateQty = useCartStore(state => state.updateQty);
  const totalPrice = useCartStore(state => state.totalPrice);
  let TotalWithoutFee = 0;
  Object.entries(itemsBySupplier).map(([supplierId]) => (
    TotalWithoutFee += totalPrice(supplierId)
  ));

  const supplierIds = Object.keys(itemsBySupplier)

  return (
    <>
      <ScrollView contentContainerStyle={{ paddingBottom: 240 }}>
        <YStack gap="$4" p="$2">
          {supplierIds.length === 0 ? (
            <Text fontSize="$6" color="$gray9">{t('cart.emptyCart', { defaultValue: 'Your cart is empty.' })}</Text>
          ) : (
            supplierIds.map(supplierId => {
              const items = itemsBySupplier[supplierId]
              const supplierName = items[0]?.supplierName || t('cart.supplier', { defaultValue: 'Supplier' })
              return (
                <YStack key={supplierId} gap="$2">
                  <Text fontSize="$6" fontWeight="600">{t('cart.from', { defaultValue: 'From' })}: {supplierName}</Text>

                  {items.map(item => (
                    <XStack
                      width="100%"
                      key={item.id}
                      jc="space-between"
                      ai="center"
                      gap="$3"
                      p="$3"
                      bw="$0.5"
                      boc="$gray5"
                      br="$4"
                    >
                      <XStack ai="center" gap="$3" width="70%">
                        <Image source={{ uri: item.product.image }} width={64} height={64} borderRadius={8} />
                        <YStack width="60%">
                          <Text fontSize="$5">{item.product.name}</Text>
                          <Text color="$gray10">₪{item.finalPrice} × {item.qty}</Text>
                          {(() => {
                            return item.supplierCategory === 'restaurants' ? (
                              <>
                                {(item.without ?? []).length > 0 && (
                                  <Text color="$gray10">
                                    {t('cart.without', { defaultValue: 'Without' })}: {(item.without ?? []).join(', ')}
                                  </Text>
                                )}
                                {(item.selectedAdditions ?? []).length > 0 && (
                                  <Text color="$gray10">
                                    {t('cart.additions', { defaultValue: 'Additions' })}: {(item.selectedAdditions ?? []).map(a => a.name).join(', ')}
                                  </Text>
                                )}
                                {(item.selectedSides ?? []).length > 0 && (
                                  <Text color="$gray10">
                                    Sides: {(item.selectedSides ?? []).map(s => s.name).join(', ')}
                                  </Text>
                                )}
                              </>
                            ) : item.supplierCategory === 'clothings' ? (
                              <>
                                <Text color="$gray10">{t('cart.size', { defaultValue: 'Size' })}: {item.selectedSize}</Text>
                                <Text color="$gray10">{t('cart.color', { defaultValue: 'Color' })}: {item.selectedColor}</Text>
                              </>
                            ) : null
                          })()}
                        </YStack>
                      </XStack>
                      <YStack gap="$2" ai="flex-end">
                        <XStack ai="center" gap="$2">
                          <Button
                            size="$2"
                            icon={<Ionicons name="remove" size={16} />}
                            onPress={() => updateQty(item.id, item.qty - 1)}
                            accessibilityLabel={t('cart.decreaseQuantity', { defaultValue: 'Decrease quantity' })}
                          />
                          <Text>{item.qty}</Text>
                          <Button
                            size="$2"
                            icon={<Ionicons name="add" size={16} />}
                            onPress={() => updateQty(item.id, item.qty + 1)}
                            accessibilityLabel={t('cart.increaseQuantity', { defaultValue: 'Increase quantity' })}
                          />
                        </XStack>
                        <Button
                          size="$2"
                          icon={<Ionicons name="trash" size={16} />}
                          onPress={() => removeItem(item.id)}
                          accessibilityLabel={t('cart.removeItem', { defaultValue: 'Remove item' })}
                        />
                      </YStack>
                    </XStack>
                  ))}

                  <XStack jc="space-between" ai="center" mt="$2">
                    <Text fontWeight="bold">{t('cart.subtotal', { defaultValue: 'Subtotal' })}:</Text>
                    <Text fontWeight="bold">₪{totalPrice(supplierId).toFixed(2)}</Text>
                  </XStack>
                  <Separator my="$2" />
                </YStack>
              )
            })
          )}
        </YStack>
      </ScrollView>

      {supplierIds.length > 0 && (
        <YStack
          gap="$4"
          p="$4"
          px="$3"
          bg="$background"
          position="absolute"
          bottom={0}
          left={0}
          right={0}
          elevation="$2"
          br="$6"
          pb={40}
        >
          <Separator my="$2" />
          <XStack jc="space-between" ai="center">
            <Text fontWeight="bold" fontSize="$5">{t('cart.total', { defaultValue: 'Total' })}</Text>
            <Text fontWeight="bold" fontSize="$5">₪{TotalWithoutFee.toFixed(2)}</Text>
          </XStack>
          <Button
            bg="$primary"
            size="$5"
            onPress={() => {
              onClose()
              router.push('/home/<USER>')
            }}
            br="$6"
            elevation="$0.75"
            hoverStyle={{ bg: "$third" }}
            pressStyle={{ bg: "$third" }}
          >
            <MotiView
              from={{ opacity: 0.7, scale: 0.97 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 200 }}
            >
              <Text color="white" fontWeight="600">
                {t('cart.proceedToCheckout', { defaultValue: 'Proceed to Checkout' })}
              </Text>
            </MotiView>
          </Button>
        </YStack>
      )}
    </>
  )
}