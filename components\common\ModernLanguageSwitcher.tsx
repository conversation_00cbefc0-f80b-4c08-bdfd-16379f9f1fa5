import React from 'react';
import { XStack, Text, Button } from 'tamagui';
import { Pressable } from 'react-native';
import { useLanguageStore } from '../../stores/languageStore';
import { Ionicons } from '@expo/vector-icons';

interface ModernLanguageSwitcherProps {
  variant?: 'compact' | 'full';
  position?: 'top-right' | 'center' | 'bottom';
  showLabels?: boolean;
}

export const ModernLanguageSwitcher = ({ 
  variant = 'compact', 
  position = 'top-right',
  showLabels = true 
}: ModernLanguageSwitcherProps) => {
  const { currentLanguage, setLanguage } = useLanguageStore();

  const languages = [
    { code: 'en', label: 'English', flag: '🇺🇸', short: 'EN' },
    { code: 'ar', label: 'العربية', flag: '🇵🇸', short: 'ع' }
  ];

  if (variant === 'compact') {
    return (
      <XStack 
        backgroundColor="rgba(255,255,255,0.15)" 
        borderRadius="$6" 
        padding="$1"
        alignItems="center"
        gap="$1"
        borderWidth={1}
        borderColor="rgba(255,255,255,0.2)"
      >
        {languages.map((lang) => (
          <Pressable
            key={lang.code}
            onPress={() => setLanguage(lang.code as 'en' | 'ar')}
            style={({ pressed }) => ({
              backgroundColor: currentLanguage === lang.code 
                ? 'rgba(255,255,255,0.9)' 
                : pressed 
                  ? 'rgba(255,255,255,0.1)' 
                  : 'transparent',
              borderRadius: 20,
              paddingHorizontal: 12,
              paddingVertical: 6,
              transform: pressed ? [{ scale: 0.95 }] : [{ scale: 1 }],
            })}
          >
            <XStack alignItems="center" gap="$1">
              <Text fontSize="$2">
                {lang.flag}
              </Text>
              {showLabels && (
                <Text 
                  fontSize="$3" 
                  fontWeight="600"
                  color={currentLanguage === lang.code ? '$primary' : 'white'}
                >
                  {lang.short}
                </Text>
              )}
            </XStack>
          </Pressable>
        ))}
      </XStack>
    );
  }

  // Full variant for larger displays
  return (
    <XStack 
      backgroundColor="$background" 
      borderRadius="$4" 
      padding="$2"
      alignItems="center"
      gap="$2"
      borderWidth={1}
      borderColor="$borderColor"
      shadowColor="$shadowColor"
      shadowOffset={{ width: 0, height: 2 }}
      shadowOpacity={0.1}
      shadowRadius={4}
      elevation={3}
    >
      {languages.map((lang) => (
        <Button
          key={lang.code}
          onPress={() => setLanguage(lang.code as 'en' | 'ar')}
          backgroundColor={currentLanguage === lang.code ? '$primary' : 'transparent'}
          color={currentLanguage === lang.code ? 'white' : '$gray11'}
          borderRadius="$3"
          paddingHorizontal="$3"
          paddingVertical="$2"
          pressStyle={{
            backgroundColor: currentLanguage === lang.code ? '$primary' : '$gray3',
            scale: 0.95
          }}
          hoverStyle={{
            backgroundColor: currentLanguage === lang.code ? '$primary' : '$gray2'
          }}
        >
          <XStack alignItems="center" gap="$2">
            <Text fontSize="$3">
              {lang.flag}
            </Text>
            <Text 
              fontSize="$3" 
              fontWeight="600"
              color={currentLanguage === lang.code ? 'white' : '$gray11'}
            >
              {lang.label}
            </Text>
          </XStack>
        </Button>
      ))}
    </XStack>
  );
};
