// Critical polyfill for globalThis - must run before any other modules
// This fixes "Cannot access 'globalThis' before initialization" errors

// Immediately define globalThis if it doesn't exist
(function() {
  'use strict';

  // Force globalThis to exist immediately
  if (typeof globalThis === 'undefined') {
    if (typeof window !== 'undefined') {
      // Browser environment
      Object.defineProperty(window, 'globalThis', {
        value: window,
        writable: true,
        enumerable: false,
        configurable: true
      });
    } else if (typeof global !== 'undefined') {
      // Node.js environment
      Object.defineProperty(global, 'globalThis', {
        value: global,
        writable: true,
        enumerable: false,
        configurable: true
      });
    } else if (typeof self !== 'undefined') {
      // Web Worker environment
      Object.defineProperty(self, 'globalThis', {
        value: self,
        writable: true,
        enumerable: false,
        configurable: true
      });
    } else {
      // Fallback - create a minimal global object
      var globalObj = {};
      if (typeof window !== 'undefined') {
        window.globalThis = globalObj;
      } else if (typeof global !== 'undefined') {
        global.globalThis = globalObj;
      } else if (typeof self !== 'undefined') {
        self.globalThis = globalObj;
      }
    }
  }

  // Ensure globalThis is available in the current scope
  if (typeof globalThis !== 'undefined') {
    // Add import.meta polyfill
    if (!globalThis.import) {
      globalThis.import = {
        meta: {
          url: typeof window !== 'undefined' ? window.location.href : 'file:///',
          env: typeof process !== 'undefined' ? process.env : {},
        }
      };
    }

    // Also add to window for browser compatibility
    if (typeof window !== 'undefined' && !window.import) {
      window.import = {
        meta: {
          url: window.location.href,
          env: {},
        }
      };
    }
  }
})();

module.exports = {};
