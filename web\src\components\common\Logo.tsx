import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showText?: boolean;
}

// Lightning T Component with realistic effects
const LightningT: React.FC<{ size: number; className?: string }> = ({ size, className = '' }) => {
  return (
    <motion.svg
      width={size}
      height={size}
      viewBox="0 0 100 100"
      className={className}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <defs>
        {/* Yellow-focused gradient for lightning T */}
        <linearGradient id="lightningGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#FCD34D" />
          <stop offset="25%" stopColor="#FBBF24" />
          <stop offset="50%" stopColor="#F59E0B" />
          <stop offset="75%" stopColor="#FBBF24" />
          <stop offset="100%" stopColor="#FCD34D" />
        </linearGradient>

        {/* Pure yellow lightning effect */}
        <linearGradient id="lightningAnimated" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#FFFFFF">
            <animate attributeName="stop-color"
              values="#FFFFFF;#FBBF24;#FCD34D;#FBBF24;#FFFFFF"
              dur="0.2s"
              repeatCount="indefinite"/>
          </stop>
          <stop offset="50%" stopColor="#FBBF24">
            <animate attributeName="stop-color"
              values="#FBBF24;#FCD34D;#FFFFFF;#FCD34D;#FBBF24"
              dur="0.2s"
              repeatCount="indefinite"/>
          </stop>
          <stop offset="100%" stopColor="#FCD34D">
            <animate attributeName="stop-color"
              values="#FCD34D;#FFFFFF;#FBBF24;#FFFFFF;#FCD34D"
              dur="0.2s"
              repeatCount="indefinite"/>
          </stop>
        </linearGradient>

        {/* Enhanced glow effect */}
        <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
          <feGaussianBlur stdDeviation="4" result="coloredBlur"/>
          <feColorMatrix in="coloredBlur" type="matrix"
            values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 2 0"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>

        {/* Electric spark effect */}
        <filter id="electricSpark" x="-100%" y="-100%" width="300%" height="300%">
          <feGaussianBlur stdDeviation="2" result="blur"/>
          <feColorMatrix in="blur" type="matrix"
            values="1 0 0 0 0  0 1 0 0 0  0 0 1 0 0  0 0 0 3 0"/>
          <feComposite in="blur" in2="SourceGraphic" operator="screen"/>
        </filter>
      </defs>

      {/* Main lightning bolt T shape - cleaner T with lightning bolt */}
      <motion.path
        d="M15 10 L85 10 L85 22 L58 22 L58 30 L70 30 L65 38 L68 45 L62 52 L66 58 L60 65 L63 72 L57 78 L60 85 L45 95 L50 88 L54 82 L51 75 L55 68 L52 62 L56 55 L53 48 L57 42 L54 35 L58 28 L42 28 L42 22 L15 22 Z"
        fill="url(#lightningGradient)"
        filter="url(#glow)"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{ pathLength: 1, opacity: 1 }}
        transition={{ duration: 1.2, delay: 0.5, ease: "easeOut" }}
      />

      {/* Lightning strike from above - realistic effect */}
      <motion.path
        d="M50 0 L52 8 L48 15 L51 22 L50 30 L65 38 L68 45 L62 52 L66 58 L60 65 L63 72 L57 78 L60 85 L45 95"
        fill="none"
        stroke="url(#lightningAnimated)"
        strokeWidth="4"
        filter="url(#electricSpark)"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{
          pathLength: [0, 1],
          opacity: [0, 1, 0.9, 0],
          strokeWidth: [2, 6, 3]
        }}
        transition={{
          duration: 0.4,
          repeat: Infinity,
          repeatDelay: 3,
          ease: "easeOut"
        }}
      />

      {/* Secondary lightning branch */}
      <motion.path
        d="M15 10 L85 10 L85 22 L58 22 L58 30 L70 30 L65 38 L68 45 L62 52 L66 58 L60 65 L63 72 L57 78 L60 85 L45 95 L50 88 L54 82 L51 75 L55 68 L52 62 L56 55 L53 48 L57 42 L54 35 L58 28 L42 28 L42 22 L15 22 Z"
        fill="none"
        stroke="url(#lightningAnimated)"
        strokeWidth="3"
        filter="url(#electricSpark)"
        initial={{ pathLength: 0, opacity: 0 }}
        animate={{
          pathLength: [0, 1, 0],
          opacity: [0, 1, 0.8, 0],
          strokeWidth: [3, 5, 2, 3]
        }}
        transition={{
          duration: 0.6,
          delay: 0.2,
          repeat: Infinity,
          repeatDelay: 3,
          ease: "easeInOut"
        }}
      />

      {/* Additional electric crackling effects */}
      <motion.circle
        cx="50"
        cy="50"
        r="2"
        fill="#FFFFFF"
        filter="url(#electricSpark)"
        initial={{ opacity: 0, scale: 0 }}
        animate={{
          opacity: [0, 1, 0],
          scale: [0, 1.5, 0]
        }}
        transition={{
          duration: 0.2,
          repeat: Infinity,
          repeatDelay: 1.8,
          delay: 0.5
        }}
      />

      <motion.circle
        cx="65"
        cy="35"
        r="1.5"
        fill="#FBBF24"
        filter="url(#electricSpark)"
        initial={{ opacity: 0, scale: 0 }}
        animate={{
          opacity: [0, 1, 0],
          scale: [0, 2, 0]
        }}
        transition={{
          duration: 0.15,
          repeat: Infinity,
          repeatDelay: 2.2,
          delay: 0.8
        }}
      />
    </motion.svg>
  );
};

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  className = '',
  showText = true
}) => {
  const [animationPhase, setAnimationPhase] = useState<'lightning' | 'split'>('lightning');

  const sizeClasses = {
    sm: { height: 32, fontSize: 'text-lg', lightning: 24 },
    md: { height: 48, fontSize: 'text-2xl', lightning: 32 },
    lg: { height: 64, fontSize: 'text-3xl', lightning: 40 },
    xl: { height: 80, fontSize: 'text-4xl', lightning: 48 }
  };

  // Enhanced font styles
  const fontStyle = {
    fontFamily: '"Orbitron", "Exo 2", "Rajdhani", "Russo One", Impact, "Arial Black", sans-serif',
    fontWeight: '900',
    letterSpacing: '0.05em',
    textShadow: '0 0 20px rgba(117, 41, 179, 0.5), 0 0 40px rgba(103, 179, 41, 0.3)'
  };

  const currentSize = sizeClasses[size];

  useEffect(() => {
    // Start with lightning, then move to split
    const timer = setTimeout(() => setAnimationPhase('split'), 3000);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  return (
    <div className={`flex items-center justify-center ${className}`} style={{ height: currentSize.height }}>
      <AnimatePresence mode="wait">
        {animationPhase === 'lightning' && (
          <motion.div
            key="lightning"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{
              opacity: 0,
              scale: 1.02,
              rotateX: 5
            }}
            transition={{
              duration: 0.6,
              ease: "easeInOut"
            }}
            className="flex items-center relative"
          >
            <span
              className={`${currentSize.fontSize} bg-gradient-to-r from-[#9333EA] via-[#7529B3] via-[#67B329] to-[#059669] bg-clip-text text-transparent relative z-10`}
              style={fontStyle}
            >
              BolTalab
            </span>

            {/* Enhanced lightning effect overlay with multiple layers */}
            <motion.div
              className="absolute inset-0 flex items-center justify-center"
              initial={{ opacity: 0, scale: 3 }}
              animate={{
                opacity: [0, 0.8, 1, 0.6, 0],
                scale: [3, 1.5, 1, 0.8, 0.3],
                rotate: [0, 2, -1, 1, 0]
              }}
              transition={{
                duration: 2,
                times: [0, 0.3, 0.5, 0.8, 1],
                ease: "easeInOut"
              }}
            >
              <div className="w-full h-full bg-gradient-to-r from-[#FBBF24] via-[#FCD34D] to-[#FFFFFF] opacity-50 blur-lg rounded-lg" />
            </motion.div>

            {/* Electric sparks around text */}
            <motion.div
              className="absolute inset-0"
              initial={{ opacity: 0 }}
              animate={{
                opacity: [0, 1, 0.5, 1, 0]
              }}
              transition={{
                duration: 1.5,
                repeat: 2,
                ease: "easeInOut"
              }}
            >
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full"
                  style={{
                    left: `${20 + i * 15}%`,
                    top: `${30 + (i % 2) * 40}%`,
                    boxShadow: '0 0 10px #FBBF24, 0 0 20px #FCD34D, 0 0 30px #FFFFFF'
                  }}
                  animate={{
                    scale: [0, 1.5, 0],
                    opacity: [0, 1, 0]
                  }}
                  transition={{
                    duration: 0.3,
                    delay: i * 0.1,
                    repeat: 3
                  }}
                />
              ))}
            </motion.div>
          </motion.div>
        )}

        {animationPhase === 'split' && (
          <motion.div
            key="split"
            initial={{ opacity: 0, scale: 0.95, y: 10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{
              duration: 0.8,
              ease: "easeOut",
              type: "spring",
              stiffness: 100,
              damping: 15
            }}
            className="flex flex-col items-center"
          >
            {/* Main logo with reduced spacing */}
            <div className="flex items-center gap-0.5">
              <motion.span
                initial={{ x: 0, opacity: 0, scale: 0.9 }}
                animate={{ x: -2, opacity: 1, scale: 1 }}
                transition={{
                  duration: 1,
                  ease: "easeOut",
                  delay: 0.2,
                  type: "spring",
                  stiffness: 120,
                  damping: 12
                }}
                className={`${currentSize.fontSize} bg-gradient-to-r from-[#9333EA] via-[#7529B3] to-[#FBBF24] bg-clip-text text-transparent`}
                style={{
                  ...fontStyle,
                  filter: 'drop-shadow(0 0 8px rgba(147, 51, 234, 0.6))'
                }}
              >
                Bol
              </motion.span>

              <motion.div
                initial={{ scale: 0, rotate: 180, opacity: 0 }}
                animate={{
                  scale: [0, 1.2, 1],
                  rotate: [180, -10, 0],
                  opacity: 1
                }}
                transition={{
                  duration: 1,
                  delay: 0.2,
                  ease: "easeOut",
                  times: [0, 0.7, 1]
                }}
                className="relative mx-0.5"
              >
                <LightningT size={currentSize.lightning} />

                {/* Glow effect around lightning */}
                <motion.div
                  className="absolute inset-0 rounded-full"
                  style={{
                    background: 'radial-gradient(circle, rgba(251, 191, 36, 0.6) 0%, rgba(252, 211, 77, 0.4) 40%, rgba(255, 255, 255, 0.2) 70%, transparent 90%)',
                    filter: 'blur(8px)'
                  }}
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />
              </motion.div>

              <motion.span
                initial={{ x: 0, opacity: 0, scale: 0.9 }}
                animate={{ x: 2, opacity: 1, scale: 1 }}
                transition={{
                  duration: 1,
                  ease: "easeOut",
                  delay: 0.4,
                  type: "spring",
                  stiffness: 120,
                  damping: 12
                }}
                className={`${currentSize.fontSize} bg-gradient-to-r from-[#FBBF24] via-[#67B329] to-[#059669] bg-clip-text text-transparent`}
                style={{
                  ...fontStyle,
                  filter: 'drop-shadow(0 0 8px rgba(103, 179, 41, 0.6))'
                }}
              >
                alab
              </motion.span>
            </div>

            {/* "Fast As Lightning" tagline */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1 }}
              className="mt-1"
            >
              <span
                className={`${size === 'xl' ? 'text-sm' : size === 'lg' ? 'text-xs' : 'text-[10px]'} font-semibold tracking-wider uppercase bg-gradient-to-r from-[#9333EA] via-[#FBBF24] to-[#059669] bg-clip-text text-transparent`}
                style={{
                  fontFamily: '"Rajdhani", "Exo 2", sans-serif',
                  letterSpacing: '0.15em',
                  filter: 'drop-shadow(0 0 4px rgba(251, 191, 36, 0.4))'
                }}
              >
                Fast As Lightning
              </span>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default Logo;
