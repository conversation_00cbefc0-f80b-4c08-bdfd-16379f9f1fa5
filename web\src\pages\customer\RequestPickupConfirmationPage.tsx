import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle, Clock, MapPin, Phone, Package, User, Home, ArrowLeft, Search,
  Truck, Shield, Star, Zap, Gift, Sparkles, Crown, Bot, Navigation,
  Calendar, DollarSign, FileText, Award, Target, TrendingUp
} from 'lucide-react';

const RequestPickupConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // Scroll and animation states
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showCelebration, setShowCelebration] = useState(true);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Hide celebration after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);
  
  // Get pickup request data from location state or use mock data
  const pickupRequest = location.state?.pickupRequest || {
    id: `PU-${Date.now().toString().slice(-6)}`,
    pickupAddress: 'Nablus, Palestine',
    deliveryAddress: 'Ramallah, Palestine',
    senderName: 'Ahmad Samer',
    senderPhone: '+970568406041',
    receiverName: 'Sara Ahmad',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    packageSize: 'small',
    packageWeight: '500g',
    price: 15,
    status: 'Pending',
    preferredTime: 'asap',
    createdAt: new Date().toISOString()
  };

  const handleTrackPickup = () => {
    navigate(`/customer/package-tracking?pickupId=${pickupRequest.id}`);
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewPackages = () => {
    navigate('/customer/packages');
  };

  const getSizeLabel = (size: string) => {
    const labels: Record<string, string> = {
      'small': 'Small (up to 30cm)',
      'medium': 'Medium (30-60cm)',
      'large': 'Large (60-100cm)',
      'extra-large': 'Extra Large (100cm+)'
    };
    return labels[size] || size;
  };

  const getTimeLabel = (time: string) => {
    if (time === 'asap') return 'As soon as possible';
    if (time === 'today') return 'Later today';
    return time;
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-green-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-green-500/30 to-emerald-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute top-1/3 right-0 w-80 h-80 bg-gradient-to-br from-teal-500/30 to-cyan-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute bottom-0 left-1/3 w-72 h-72 bg-gradient-to-br from-yellow-500/20 to-orange-600/20 rounded-full blur-3xl"
        />

        {/* Floating particles */}
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: 3 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 5,
            }}
            className="absolute w-1 h-1 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Celebration Confetti */}
      <AnimatePresence>
        {showCelebration && (
          <div className="fixed inset-0 pointer-events-none z-30">
            {[...Array(50)].map((_, i) => (
              <motion.div
                key={i}
                initial={{
                  y: -100,
                  x: Math.random() * window.innerWidth,
                  rotate: 0,
                  opacity: 1
                }}
                animate={{
                  y: window.innerHeight + 100,
                  rotate: 360 * 3,
                  opacity: 0
                }}
                transition={{
                  duration: 3 + Math.random() * 2,
                  delay: Math.random() * 2,
                  ease: "easeOut"
                }}
                className={`absolute w-3 h-3 ${
                  ['bg-yellow-400', 'bg-green-400', 'bg-blue-400', 'bg-purple-400', 'bg-pink-400'][Math.floor(Math.random() * 5)]
                } rounded-full`}
              />
            ))}
          </div>
        )}
      </AnimatePresence>

      {/* Sticky Header with Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0 transition-all duration-500"
        animate={{
          top: isHeaderCompact ? "0px" : "64px",
          zIndex: isHeaderCompact ? 55 : 35,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{ duration: 0.3 }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "2rem",
              paddingBottom: isHeaderCompact ? "1rem" : "2rem",
            }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-between"
          >
            <div className="flex items-center gap-4">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate(-1)}
                className="p-3 bg-white/10 backdrop-blur-xl rounded-xl border border-white/20 hover:bg-white/20 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white" />
              </motion.button>

              <motion.div
                animate={{
                  fontSize: isHeaderCompact ? "1.5rem" : "2rem",
                }}
                transition={{ duration: 0.3 }}
                className="font-bold text-white"
              >
                Pickup Confirmed
              </motion.div>
            </div>

            {/* Compact Search when header is compact */}
            <AnimatePresence>
              {isHeaderCompact && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="flex-1 max-w-md mx-8"
                >
                  <div className="bg-white/10 backdrop-blur-xl rounded-xl border border-white/20">
                    <div className="flex items-center gap-3 p-3">
                      <Search size={18} className="text-white/60" />
                      <input
                        type="text"
                        placeholder="Search..."
                        className="flex-1 bg-transparent text-white placeholder-white/50 outline-none text-sm"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        </div>
      </motion.div>

      {/* Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="relative z-10 pt-24 pb-16"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* Success Animation */}
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              type: "spring",
              stiffness: 200,
              damping: 10
            }}
            className="mb-8"
          >
            <div className="relative inline-block">
              {/* Pulsing rings */}
              <motion.div
                animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute inset-0 bg-green-400 rounded-full"
              />
              <motion.div
                animate={{ scale: [1, 1.3, 1], opacity: [0.7, 0, 0.7] }}
                transition={{ duration: 2, repeat: Infinity, delay: 0.5 }}
                className="absolute inset-0 bg-emerald-400 rounded-full"
              />

              {/* Main success icon */}
              <div className="relative bg-white rounded-full p-8 shadow-2xl border-4 border-green-400">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, delay: 1 }}
                >
                  <CheckCircle className="w-20 h-20 text-green-500" />
                </motion.div>
              </div>

              {/* Sparkles around the icon */}
              {[...Array(8)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: [0, 1, 0],
                    opacity: [0, 1, 0],
                    rotate: [0, 180, 360]
                  }}
                  transition={{
                    duration: 2,
                    delay: 1 + i * 0.1,
                    repeat: Infinity,
                    repeatDelay: 3
                  }}
                  className="absolute w-4 h-4 text-yellow-400"
                  style={{
                    top: `${20 + 60 * Math.cos((i * Math.PI * 2) / 8)}%`,
                    left: `${20 + 60 * Math.sin((i * Math.PI * 2) / 8)}%`,
                  }}
                >
                  <Sparkles className="w-4 h-4" />
                </motion.div>
              ))}
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mb-8"
          >
            <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-100 bg-clip-text text-transparent mb-4">
              Success!
            </h1>
            <h2 className="text-2xl md:text-3xl font-semibold text-white mb-4">
              Pickup Request Confirmed
            </h2>
            <p className="text-white/70 text-xl font-medium max-w-2xl mx-auto">
              Your package pickup has been scheduled. We'll find the best driver and keep you updated every step of the way.
            </p>
          </motion.div>

          {/* Quick Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto mb-12"
          >
            {[
              { icon: Zap, label: 'Fast Response', value: '< 15 mins', color: 'from-yellow-500 to-orange-500' },
              { icon: Shield, label: 'Fully Insured', value: 'Up to ₪500', color: 'from-blue-500 to-cyan-500' },
              { icon: Star, label: 'Top Rated', value: '4.9/5 Stars', color: 'from-purple-500 to-pink-500' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-6"
              >
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-3`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-white">{stat.value}</div>
                <div className="text-white/60 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen px-4 sm:px-6 lg:px-8 pb-20">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">

            {/* Enhanced Details Card */}
            <div className="xl:col-span-2 space-y-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl"
              >
                <div className="flex items-center gap-4 mb-8">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg shadow-green-500/25"
                  >
                    <Package className="w-8 h-8 text-white" />
                  </motion.div>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">Pickup Request Details</h2>
                    <p className="text-white/70">Request ID: <span className="font-mono text-green-400">{pickupRequest.id}</span></p>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Location Information */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                      <MapPin className="w-5 h-5 text-green-400" />
                      Location Details
                    </h3>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center">
                          <MapPin className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Pickup Location</p>
                          <p className="text-white/70 text-sm">{pickupRequest.pickupAddress}</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-start gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                          <Target className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Delivery Location</p>
                          <p className="text-white/70 text-sm">{pickupRequest.deliveryAddress}</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center">
                          <Clock className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Preferred Time</p>
                          <p className="text-white/70 text-sm">{getTimeLabel(pickupRequest.preferredTime)}</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>

                  {/* Contact & Package Information */}
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                      <User className="w-5 h-5 text-blue-400" />
                      Contact & Package Info
                    </h3>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="font-semibold text-white mb-1">Sender</p>
                          <p className="text-white/70 text-sm">{pickupRequest.senderName}</p>
                          <p className="text-white/60 text-xs">{pickupRequest.senderPhone}</p>
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Receiver</p>
                          <p className="text-white/70 text-sm">{pickupRequest.receiverName}</p>
                          <p className="text-white/60 text-xs">{pickupRequest.receiverPhone}</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.3 }}
                      className="bg-white/5 rounded-xl p-4 border border-white/10"
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-teal-500 rounded-lg flex items-center justify-center">
                          <Package className="w-4 h-4 text-white" />
                        </div>
                        <div>
                          <p className="font-semibold text-white mb-1">Package Details</p>
                          <p className="text-white/70 text-sm">{pickupRequest.packageType}</p>
                          <p className="text-white/60 text-xs">{getSizeLabel(pickupRequest.packageSize)} • {pickupRequest.packageWeight}</p>
                        </div>
                      </div>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.4 }}
                      className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 rounded-xl p-4 border border-green-400/30"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center">
                            <DollarSign className="w-4 h-4 text-white" />
                          </div>
                          <div>
                            <p className="font-semibold text-white">Service Fee</p>
                            <p className="text-white/70 text-sm">Total cost</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-white">₪{pickupRequest.price}</p>
                          <p className="text-green-400 text-sm">Confirmed</p>
                        </div>
                      </div>
                    </motion.div>
                  </div>
                </div>

                {/* Status Banner */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  className="mt-8 p-4 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 rounded-xl border border-yellow-400/30"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <motion.div
                        animate={{ rotate: [0, 360] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg flex items-center justify-center"
                      >
                        <Clock className="w-4 h-4 text-white" />
                      </motion.div>
                      <div>
                        <p className="font-semibold text-white">Current Status</p>
                        <p className="text-white/70 text-sm">We're processing your request</p>
                      </div>
                    </div>
                    <div className="px-4 py-2 bg-yellow-400 text-yellow-900 rounded-full text-sm font-bold">
                      {pickupRequest.status}
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            </div>

            {/* Enhanced Sidebar */}
            <div className="xl:col-span-1">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 shadow-2xl sticky top-8"
              >
                <h3 className="text-xl font-bold text-white mb-6 flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.1, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                  >
                    <Navigation className="w-6 h-6 text-white" />
                  </motion.div>
                  What's Next?
                </h3>

                {/* Next Steps */}
                <div className="space-y-4 mb-8">
                  {[
                    {
                      icon: Bot,
                      title: 'Driver Assignment',
                      description: 'We\'ll find the best driver for your pickup',
                      time: '5-15 mins',
                      status: 'pending'
                    },
                    {
                      icon: Phone,
                      title: 'Driver Contact',
                      description: 'Driver will call you before pickup',
                      time: '15-30 mins',
                      status: 'upcoming'
                    },
                    {
                      icon: Truck,
                      title: 'Package Pickup',
                      description: 'Driver arrives at pickup location',
                      time: '30-60 mins',
                      status: 'upcoming'
                    },
                    {
                      icon: TrendingUp,
                      title: 'Live Tracking',
                      description: 'Track your package in real-time',
                      time: 'Ongoing',
                      status: 'upcoming'
                    }
                  ].map((step, index) => (
                    <motion.div
                      key={step.title}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
                      className={`p-4 rounded-xl border transition-all duration-300 ${
                        step.status === 'pending'
                          ? 'bg-yellow-500/20 border-yellow-400/30'
                          : 'bg-white/5 border-white/10'
                      }`}
                    >
                      <div className="flex items-start gap-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          step.status === 'pending'
                            ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                            : 'bg-white/10'
                        }`}>
                          <step.icon className="w-4 h-4 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-semibold text-white text-sm">{step.title}</p>
                            <span className="text-xs text-white/60">{step.time}</span>
                          </div>
                          <p className="text-white/70 text-xs">{step.description}</p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Action Buttons */}
                <div className="space-y-4">
                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleTrackPickup}
                    className="w-full py-4 bg-gradient-to-r from-green-500 to-emerald-500 text-white font-bold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg shadow-green-500/25"
                  >
                    <Clock className="w-5 h-5" />
                    Track Pickup
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleViewPackages}
                    className="w-full py-4 bg-white/10 border border-white/20 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:bg-white/20"
                  >
                    <Package className="w-5 h-5" />
                    View All Packages
                  </motion.button>

                  <motion.button
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.9 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={handleGoHome}
                    className="w-full py-4 bg-white/10 border border-white/20 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:bg-white/20"
                  >
                    <Home className="w-5 h-5" />
                    Back to Home
                  </motion.button>
                </div>

                {/* Support Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 1.0 }}
                  className="mt-8 p-4 bg-white/5 rounded-xl border border-white/10"
                >
                  <div className="text-center">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Award className="w-6 h-6 text-white" />
                    </div>
                    <p className="text-white font-semibold mb-2">Need Help?</p>
                    <p className="text-white/60 text-sm mb-4">Our support team is here 24/7</p>
                    <button className="w-full py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg text-sm">
                      Contact Support
                    </button>
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestPickupConfirmationPage;
