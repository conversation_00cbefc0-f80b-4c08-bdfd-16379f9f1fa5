import { Animated } from 'react-native';
import { useEffect, useRef } from 'react';

export default function LoginLogo() {
  const fadeAnim = useRef(new Animated.Value(0)).current

  useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1500,
      useNativeDriver: true,
    }).start()
  }, [])

  return (
    <Animated.Image
      source={require('../assets/wasel-high-resolution-logo-transparent.png')}
      style={{
        width: 350,
        height: 110,
        opacity: fadeAnim,
        alignSelf: 'center',
      }}
    />
  )
}