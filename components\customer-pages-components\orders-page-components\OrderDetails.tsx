import { ScrollView } from 'react-native'
import { useLocalSearchParams, useRouter } from 'expo-router'
import { View, Text, YStack, XStack, Card, Button, Separator, H2 } from 'tamagui'
import { Ionicons, MaterialIcons } from '@expo/vector-icons'
import { useMyOrdersStore } from './useMyOrdersStore'
import { MotiView } from 'moti'

export function OrderDetails() {
  const { id } = useLocalSearchParams<{ id: string }>()
  const router = useRouter()

  const order = useMyOrdersStore((s) =>
    s.orders.find((o) => o.id.toString() === id)
  )

  if (!order) {
    return (
      <YStack f={1} jc="center" ai="center" p="$4">
        <Text fontSize="$6">Order not found</Text>
        <Button mt="$4" onPress={() => router.back()}>Go Back</Button>
      </YStack>
    )
  }

  const statusColor =
    order.status === 'Delivered' ? '$green10' :
    order.status === 'On the Way' ? '$orange10' :
    '$blue10'

  return (
    <ScrollView contentContainerStyle={{ paddingBottom: 40 }}>
      {/* Header */}
      <View
        style={{
          paddingVertical: 40,
          paddingHorizontal: 24,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
            Order #{order.id}
          </Text>
          <XStack jc="center" mt="$2">
            <Text bg={statusColor} px="$3" py="$1" br="$4" color="white" fontSize="$2">
              {order.status}
            </Text>
          </XStack>
        </MotiView>
      </View>

      <YStack p="$4" gap="$4">
        {/* Estimated Time */}
        <Card p="$4" br="$6" shadowColor="#000" shadowOpacity={0.08}>
          <XStack ai="center" gap="$2">
            <Ionicons name="time-outline" size={20} color="#7529B3" />
            <Text fontWeight="600" fontSize="$5">Estimated Delivery:</Text>
          </XStack>
          <Text mt="$2" color="$gray10">{order.estimatedTime}</Text>
        </Card>

        {/* Items */}
        <Card p="$4" br="$6" shadowColor="#000" shadowOpacity={0.08}>
          <XStack ai="center" gap="$2">
            <Ionicons name="cube-outline" size={20} color="#7529B3" />
            <Text fontWeight="600" fontSize="$5">Items</Text>
          </XStack>
          <YStack mt="$3" gap="$2">
            {order.items.map((item, i) => (
              <XStack key={i} jc="space-between">
                <Text>{item.qty} × {item.product.name}</Text>
                <Text>₪{(item.finalPrice * item.qty).toFixed(2)}</Text>
              </XStack>
            ))}
          </YStack>
        </Card>

        {/* Delivery Info */}
        <Card p="$4" br="$6" shadowColor="#000" shadowOpacity={0.08}>
          <XStack ai="center" gap="$2">
            <Ionicons name="location-outline" size={20} color="#7529B3" />
            <Text fontWeight="600" fontSize="$5">Delivery Info</Text>
          </XStack>
          <YStack mt="$3" gap="$1">
            <Text>📍 {order.address?.address}</Text>
            <Text>📞 {order.phone}</Text>
            {order.note && <Text>📝 {order.note}</Text>}
          </YStack>
        </Card>

        {/* Payment */}
        <Card p="$4" br="$6" shadowColor="#000" shadowOpacity={0.08}>
          <XStack ai="center" gap="$2">
            <Ionicons name="card-outline" size={20} color="#7529B3" />
            <Text fontWeight="600" fontSize="$5">Payment Method</Text>
          </XStack>
          <Text mt="$2">{order.paymentMethod}</Text>
        </Card>

        {/* Summary */}
        <Card p="$4" br="$6" shadowColor="#000" shadowOpacity={0.08}>
          <XStack ai="center" gap="$2">
            <Ionicons name="receipt-outline" size={20} color="#7529B3" />
            <Text fontWeight="600" fontSize="$5">Summary</Text>
          </XStack>
          <YStack mt="$3" gap="$2">
            <XStack jc="space-between"><Text>Subtotal</Text><Text>₪{order.subTotal.toFixed(2)}</Text></XStack>
            <XStack jc="space-between"><Text>Delivery Fee</Text><Text>₪{order.deliveryFee.toFixed(2)}</Text></XStack>
            {order.promo && (
              <XStack jc="space-between"><Text>Promo</Text><Text color="$green10">-₪{order.promo}</Text></XStack>
            )}
            <Separator my="$2" />
            <XStack jc="space-between">
              <Text fontWeight="700">Total</Text>
              <Text fontWeight="700">₪{order.total.toFixed(2)}</Text>
            </XStack>
          </YStack>
        </Card>

        {/* Track Button */}
        <Button mt="$3" size="$5" bg="$primary" color="white" br="$10" hoverStyle={{ bg: "$third" }} pressStyle={{ bg: "$third" }} onPress={() => router.push({pathname: "/(customer-pages)/orders/order-tracking", params: {id: id}})}>
          Track Order
        </Button>
        {/* Back Button */}
        <Button size="$5" bg="$gray6" br="$10" onPress={() => router.back()}>
          Back to My Orders
        </Button>
      </YStack>
    </ScrollView>
  )
}
