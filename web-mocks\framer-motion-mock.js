// Mock framer-motion for web compatibility
// This provides basic functionality without the complex dependencies

import React from 'react';

// Mock motion components
export const motion = {
  div: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('div', { ...restProps, ref });
  }),
  span: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('span', { ...restProps, ref });
  }),
  p: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('p', { ...restProps, ref });
  }),
  h1: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('h1', { ...restProps, ref });
  }),
  h2: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('h2', { ...restProps, ref });
  }),
  h3: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('h3', { ...restProps, ref });
  }),
  button: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('button', { ...restProps, ref });
  }),
  img: React.forwardRef((props, ref) => {
    const { animate, initial, transition, whileHover, whileTap, ...restProps } = props;
    return React.createElement('img', { ...restProps, ref });
  }),
};

// Mock AnimatePresence
export const AnimatePresence = ({ children, ...props }) => {
  return React.createElement(React.Fragment, null, children);
};

// Mock useAnimation
export const useAnimation = () => ({
  start: () => Promise.resolve(),
  stop: () => {},
  set: () => {},
});

// Mock useMotionValue
export const useMotionValue = (initial) => ({
  get: () => initial,
  set: () => {},
  on: () => () => {},
});

// Mock useTransform
export const useTransform = (value, input, output) => ({
  get: () => output[0],
  set: () => {},
  on: () => () => {},
});

// Mock useSpring
export const useSpring = (value, config) => ({
  get: () => value,
  set: () => {},
  on: () => () => {},
});

// Mock useCycle
export const useCycle = (...items) => [items[0], () => {}];

// Mock usePresence
export const usePresence = () => [true, () => {}];

// Mock useIsPresent
export const useIsPresent = () => true;

// Mock Variants
export const Variants = {};

// Mock Transition
export const Transition = {};

// Mock Easing
export const easeIn = 'ease-in';
export const easeOut = 'ease-out';
export const easeInOut = 'ease-in-out';
export const linear = 'linear';

// Mock spring function
export const spring = (config) => config;

// Mock tween function
export const tween = (config) => config;

// Mock keyframes function
export const keyframes = (config) => config;

// Mock inertia function
export const inertia = (config) => config;

// Mock animate function
export const animate = (element, keyframes, options) => {
  return Promise.resolve();
};

// Mock stagger function
export const stagger = (delay, options) => delay;

// Mock delay function
export const delay = (time) => time;

// Mock when function
export const when = (condition, definition) => definition;

// Export default
export default {
  motion,
  AnimatePresence,
  useAnimation,
  useMotionValue,
  useTransform,
  useSpring,
  useCycle,
  usePresence,
  useIsPresent,
  animate,
  stagger,
  delay,
  when,
  easeIn,
  easeOut,
  easeInOut,
  linear,
  spring,
  tween,
  keyframes,
  inertia,
};
