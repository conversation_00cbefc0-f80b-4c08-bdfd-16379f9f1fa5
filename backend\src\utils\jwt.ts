import jwt from 'jsonwebtoken';
import { JWTPayload } from '../types';

export class JWTUtils {
  private static accessTokenSecret = process.env.JWT_SECRET!;
  private static refreshTokenSecret = process.env.JWT_REFRESH_SECRET!;
  private static accessTokenExpiry = '7d';
  private static refreshTokenExpiry = '30d';

  static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    if (!this.accessTokenSecret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }

    return jwt.sign(payload, this.accessTokenSecret, {
      expiresIn: this.accessTokenExpiry,
    } as any);
  }

  static generateRefreshToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
    if (!this.refreshTokenSecret) {
      throw new Error('JWT_REFRESH_SECRET is not defined in environment variables');
    }

    return jwt.sign(payload, this.refreshTokenSecret, {
      expiresIn: this.refreshTokenExpiry,
    } as any);
  }

  static verifyAccessToken(token: string): JWTPayload {
    if (!this.accessTokenSecret) {
      throw new Error('JWT_SECRET is not defined in environment variables');
    }
    
    try {
      return jwt.verify(token, this.accessTokenSecret) as JWTPayload;
    } catch (error) {
      throw new Error('Invalid or expired access token');
    }
  }

  static verifyRefreshToken(token: string): JWTPayload {
    if (!this.refreshTokenSecret) {
      throw new Error('JWT_REFRESH_SECRET is not defined in environment variables');
    }
    
    try {
      return jwt.verify(token, this.refreshTokenSecret) as JWTPayload;
    } catch (error) {
      throw new Error('Invalid or expired refresh token');
    }
  }

  static generateTokenPair(payload: Omit<JWTPayload, 'iat' | 'exp'>) {
    return {
      accessToken: this.generateAccessToken(payload),
      refreshToken: this.generateRefreshToken(payload),
    };
  }

  static decodeToken(token: string): JWTPayload | null {
    try {
      return jwt.decode(token) as JWTPayload;
    } catch (error) {
      return null;
    }
  }
}
