import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  MapPin,
  User,
  Phone,
  Clock,
  ArrowLeft,
  Navigation,
  Truck,
  CheckCircle,
  RotateCcw,
  Calendar,
  Hash,
  Star,
  Zap,
  Activity,
  Eye,
  MessageCircle,
  Shield,
  DollarSign,
  Timer,
  Route,
  FileText,
  Award
} from 'lucide-react';

// Mock data - in real app this would come from API
const mockPackageDetails = {
  'PKG_1752326131054_abc123': {
    id: 'PKG_1752326131054_abc123',
    type: 'sent',
    createdAt: '2025-01-12T10:30:00Z',
    pickup: {
      address: 'Askar Camp - Mosque immigrants, Nablus',
      lat: 32.2211,
      lng: 35.2544,
      contactPerson: '<PERSON>',
      contactPhone: '+970595956014'
    },
    dropoff: {
      address: 'An-Najah National University, Nablus',
      lat: 32.2278,
      lng: 35.2542,
      contact<PERSON>erson: '<PERSON>',
      contactPhone: '+970599123456'
    },
    receiverName: '<PERSON>',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    packageSize: 'Small',
    packageWeight: '500g',
    packageValue: '₪150',
    status: 'Delivered',
    estimatedTime: '30-45 mins',
    actualDeliveryTime: '35 mins',
    driverName: 'Omar Hassan',
    driverPhone: '0597654321',
    driverRating: 4.8,
    cost: 25,
    insurance: true,
    urgency: 'standard',
    notes: 'Handle with care - important documents',
    trackingHistory: [
      { status: 'Package Created', time: '2025-01-12T10:30:00Z', description: 'Package request submitted' },
      { status: 'Driver Assigned', time: '2025-01-12T10:45:00Z', description: 'Omar Hassan assigned as driver' },
      { status: 'Picked Up', time: '2025-01-12T11:00:00Z', description: 'Package picked up from sender' },
      { status: 'In Transit', time: '2025-01-12T11:15:00Z', description: 'Package on the way to destination' },
      { status: 'Delivered', time: '2025-01-12T11:35:00Z', description: 'Package delivered successfully' }
    ]
  },
  'PKG_1752326131055_def456': {
    id: 'PKG_1752326131055_def456',
    type: 'sent',
    createdAt: '2025-01-12T14:15:00Z',
    pickup: {
      address: 'Nablus City Center',
      lat: 32.2211,
      lng: 35.2544,
      contactPerson: 'Fatima Ahmad',
      contactPhone: '+970568987654'
    },
    dropoff: {
      address: 'Balata Refugee Camp, Nablus',
      lat: 32.2100,
      lng: 35.2600,
      contactPerson: 'Fatima Ahmad',
      contactPhone: '+970568987654'
    },
    receiverName: 'Fatima Ahmad',
    receiverPhone: '+970568987654',
    packageType: 'Electronics',
    packageSize: 'Medium',
    packageWeight: '1.2kg',
    packageValue: '₪800',
    status: 'On the Way',
    estimatedTime: '20-30 mins',
    actualDeliveryTime: null,
    driverName: 'Mohammed Ali',
    driverPhone: '0599123456',
    driverRating: 4.9,
    cost: 18,
    insurance: true,
    urgency: 'express',
    notes: 'Fragile electronics - handle carefully',
    trackingHistory: [
      { status: 'Package Created', time: '2025-01-12T14:15:00Z', description: 'Package request submitted' },
      { status: 'Driver Assigned', time: '2025-01-12T14:20:00Z', description: 'Mohammed Ali assigned as driver' },
      { status: 'Picked Up', time: '2025-01-12T14:35:00Z', description: 'Package picked up from sender' },
      { status: 'In Transit', time: '2025-01-12T14:50:00Z', description: 'Package on the way to destination' }
    ]
  },
  'REQ_1752326131056_ghi789': {
    id: 'REQ_1752326131056_ghi789',
    type: 'pickup',
    createdAt: '2025-01-12T16:45:00Z',
    pickup: {
      address: 'Rafidia, Nablus',
      lat: 32.2278,
      lng: 35.2542,
      contactPerson: 'Ahmad Samer',
      contactPhone: '+970595956014'
    },
    packageType: 'Clothing',
    packageSize: 'Large',
    packageWeight: '2.5kg',
    packageValue: '₪300',
    status: 'Preparing',
    estimatedTime: '45-60 mins',
    actualDeliveryTime: null,
    driverName: 'Ahmad Samer',
    driverPhone: '0595956014',
    driverRating: 4.7,
    cost: 15,
    insurance: false,
    urgency: 'standard',
    notes: 'Please call when you arrive',
    trackingHistory: [
      { status: 'Pickup Requested', time: '2025-01-12T16:45:00Z', description: 'Pickup request submitted' },
      { status: 'Driver Assigned', time: '2025-01-12T16:50:00Z', description: 'Ahmad Samer assigned as driver' },
      { status: 'Preparing', time: '2025-01-12T17:00:00Z', description: 'Driver preparing for pickup' }
    ]
  }
};

const PackageDetailsPage: React.FC = () => {
  const { packageId } = useParams<{ packageId: string }>();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const packageType = searchParams.get('type') || 'sent';
  
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'tracking' | 'driver'>('details');

  // Handle scroll for header animation
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  const packageData = packageId ? mockPackageDetails[packageId as keyof typeof mockPackageDetails] : null;

  if (!packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Package size={64} className="text-gray-300 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-700 mb-2">Package Not Found</h2>
          <p className="text-gray-500 mb-6">The package you're looking for doesn't exist.</p>
          <button
            onClick={() => navigate('/customer/packages')}
            className="px-6 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            Back to Packages
          </button>
        </div>
      </div>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={24} className="text-green-400" />;
      case 'On the Way': case 'In Transit': return <Truck size={24} className="text-orange-400" />;
      case 'Preparing': return <Clock size={24} className="text-blue-400" />;
      default: return <Package size={24} className="text-yellow-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-300 bg-green-500/20 border-green-400/50';
      case 'On the Way': case 'In Transit': return 'text-orange-300 bg-orange-500/20 border-orange-400/50';
      case 'Preparing': return 'text-blue-300 bg-blue-500/20 border-blue-400/50';
      default: return 'text-yellow-300 bg-yellow-500/20 border-yellow-400/50';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-indigo-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-cyan-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-emerald-500/20 to-teal-500/20 rounded-full blur-3xl"
          />
        </div>

        {/* Main Content */}
        <div className="relative z-10">
          {/* Hero Header */}
          <div className="pt-20 pb-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-7xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
                className="text-center mb-12"
              >
                <div className="inline-flex items-center gap-4 mb-6">
                  <motion.div
                    animate={{ rotate: [0, 360] }}
                    transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                    className="w-20 h-20 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center shadow-2xl"
                  >
                    {packageData.type === 'sent' ? (
                      <Package className="text-white" size={36} />
                    ) : (
                      <RotateCcw className="text-white" size={36} />
                    )}
                  </motion.div>
                  <div className="text-left">
                    <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
                      Package #{packageData.id.slice(-8)}
                    </h1>
                    <motion.div
                      animate={{ width: ["0%", "100%"] }}
                      transition={{ duration: 2, delay: 0.5 }}
                      className="h-1 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full mt-2"
                    />
                  </div>
                </div>

                {/* Status Badge */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                  className={`inline-flex items-center gap-3 px-6 py-3 rounded-2xl text-lg font-bold border-2 ${getStatusColor(packageData.status)} backdrop-blur-sm mb-6`}
                >
                  {getStatusIcon(packageData.status)}
                  {packageData.status}
                </motion.div>

                <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                  {packageData.type === 'sent' ? 'Sent Package' : 'Pickup Request'} • {packageData.packageType} • Created {formatDate(packageData.createdAt).date}
                </p>
              </motion.div>

              {/* Navigation Tabs */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="mb-8"
              >
                <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-2 border border-white/20 max-w-md mx-auto">
                  <div className="flex gap-2">
                    {[
                      { key: 'details', label: 'Details', icon: FileText },
                      { key: 'tracking', label: 'Tracking', icon: Route },
                      { key: 'driver', label: 'Driver', icon: Truck }
                    ].map((tab) => (
                      <motion.button
                        key={tab.key}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => setActiveTab(tab.key as any)}
                        className={`flex-1 px-4 py-3 rounded-xl text-sm font-semibold transition-all duration-300 flex items-center justify-center gap-2 ${
                          activeTab === tab.key
                            ? 'bg-gradient-to-r from-primary-500 to-secondary-500 text-white shadow-lg'
                            : 'text-white hover:bg-white/20'
                        }`}
                      >
                        <tab.icon size={16} />
                        {tab.label}
                      </motion.button>
                    ))}
                  </div>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Content Sections */}
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
            <AnimatePresence mode="wait">
              {activeTab === 'details' && (
                <motion.div
                  key="details"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                  className="grid lg:grid-cols-2 gap-8"
                >
                  {/* Package Information */}
                  <div className="space-y-6">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-500/30 rounded-xl flex items-center justify-center">
                          <Package size={20} />
                        </div>
                        Package Information
                      </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm">Type</p>
                            <p className="text-white font-semibold">{packageData.packageType}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Size</p>
                            <p className="text-white font-semibold">{packageData.packageSize}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Weight</p>
                            <p className="text-white font-semibold">{packageData.packageWeight}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Value</p>
                            <p className="text-white font-semibold">{packageData.packageValue}</p>
                          </div>
                        </div>
                        <div className="pt-4 border-t border-white/20">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-300">Total Cost</span>
                            <span className="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                              ₪{packageData.cost}
                            </span>
                          </div>
                        </div>
                        {packageData.insurance && (
                          <div className="flex items-center gap-2 text-green-400">
                            <Shield size={16} />
                            <span className="text-sm">Insurance Included</span>
                          </div>
                        )}
                        {packageData.urgency === 'express' && (
                          <div className="flex items-center gap-2 text-yellow-400">
                            <Zap size={16} />
                            <span className="text-sm">Express Delivery</span>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Location Information */}
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-500/30 rounded-xl flex items-center justify-center">
                          <MapPin size={20} />
                        </div>
                        Locations
                      </h3>
                      <div className="space-y-6">
                        {/* Pickup Location */}
                        <div>
                          <div className="flex items-center gap-2 mb-3">
                            <MapPin size={16} className="text-blue-400" />
                            <span className="text-gray-300 font-medium">Pickup Location</span>
                          </div>
                          <div className="bg-white/10 rounded-xl p-4">
                            <p className="text-white font-semibold mb-2">{packageData.pickup.address}</p>
                            <div className="text-sm text-gray-300">
                              <p>Contact: {packageData.pickup.contactPerson}</p>
                              <p>Phone: {packageData.pickup.contactPhone}</p>
                            </div>
                          </div>
                        </div>

                        {/* Dropoff Location (for sent packages) */}
                        {packageData.type === 'sent' && 'dropoff' in packageData && (
                          <div>
                            <div className="flex items-center gap-2 mb-3">
                              <Navigation size={16} className="text-green-400" />
                              <span className="text-gray-300 font-medium">Delivery Location</span>
                            </div>
                            <div className="bg-white/10 rounded-xl p-4">
                              <p className="text-white font-semibold mb-2">{packageData.dropoff.address}</p>
                              <div className="text-sm text-gray-300">
                                <p>Contact: {packageData.dropoff.contactPerson}</p>
                                <p>Phone: {packageData.dropoff.contactPhone}</p>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                  {/* Timing and Notes */}
                  <div className="space-y-6">
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                    >
                      <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                        <div className="w-10 h-10 bg-purple-500/30 rounded-xl flex items-center justify-center">
                          <Timer size={20} />
                        </div>
                        Timing Information
                      </h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-gray-300 text-sm">Created</p>
                            <p className="text-white font-semibold">{formatDate(packageData.createdAt).date}</p>
                            <p className="text-gray-400 text-xs">{formatDate(packageData.createdAt).time}</p>
                          </div>
                          <div>
                            <p className="text-gray-300 text-sm">Estimated Time</p>
                            <p className="text-white font-semibold">{packageData.estimatedTime}</p>
                          </div>
                        </div>
                        {packageData.actualDeliveryTime && (
                          <div className="pt-4 border-t border-white/20">
                            <p className="text-gray-300 text-sm">Actual Delivery Time</p>
                            <p className="text-green-400 font-semibold">{packageData.actualDeliveryTime}</p>
                          </div>
                        )}
                      </div>
                    </motion.div>

                    {/* Notes */}
                    {packageData.notes && (
                      <motion.div
                        whileHover={{ scale: 1.02 }}
                        className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                      >
                        <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                          <div className="w-10 h-10 bg-pink-500/30 rounded-xl flex items-center justify-center">
                            <MessageCircle size={20} />
                          </div>
                          Special Notes
                        </h3>
                        <div className="bg-white/10 rounded-xl p-4">
                          <p className="text-gray-200 leading-relaxed">{packageData.notes}</p>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </motion.div>
              )}

              {activeTab === 'tracking' && (
                <motion.div
                  key="tracking"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                  >
                    <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                      <div className="w-10 h-10 bg-blue-500/30 rounded-xl flex items-center justify-center">
                        <Route size={20} />
                      </div>
                      Tracking History
                    </h3>
                    <div className="space-y-4">
                      {packageData.trackingHistory.map((event, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-start gap-4 p-4 bg-white/10 rounded-xl"
                        >
                          <div className="w-3 h-3 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                          <div className="flex-1">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="text-white font-semibold">{event.status}</h4>
                              <span className="text-gray-300 text-sm">
                                {formatDate(event.time).time}
                              </span>
                            </div>
                            <p className="text-gray-300 text-sm">{event.description}</p>
                            <p className="text-gray-400 text-xs mt-1">
                              {formatDate(event.time).date}
                            </p>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </motion.div>
              )}

              {activeTab === 'driver' && (
                <motion.div
                  key="driver"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    whileHover={{ scale: 1.01 }}
                    className="bg-white/10 backdrop-blur-lg rounded-2xl p-6 border border-white/20"
                  >
                    <h3 className="text-2xl font-bold text-white mb-6 flex items-center gap-3">
                      <div className="w-10 h-10 bg-yellow-500/30 rounded-xl flex items-center justify-center">
                        <Truck size={20} />
                      </div>
                      Driver Information
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <p className="text-gray-300 text-sm">Driver Name</p>
                          <p className="text-white font-semibold text-lg">{packageData.driverName}</p>
                        </div>
                        <div>
                          <p className="text-gray-300 text-sm">Phone Number</p>
                          <p className="text-white font-semibold">{packageData.driverPhone}</p>
                        </div>
                        <div>
                          <p className="text-gray-300 text-sm">Rating</p>
                          <div className="flex items-center gap-2">
                            <div className="flex items-center">
                              {[...Array(5)].map((_, i) => (
                                <Star
                                  key={i}
                                  size={16}
                                  className={i < Math.floor(packageData.driverRating) ? 'text-yellow-400 fill-current' : 'text-gray-400'}
                                />
                              ))}
                            </div>
                            <span className="text-white font-semibold">{packageData.driverRating}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center justify-center">
                        <motion.div
                          animate={{ scale: [1, 1.1, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="w-32 h-32 bg-gradient-to-br from-yellow-500/30 to-orange-500/30 rounded-full flex items-center justify-center"
                        >
                          <Truck size={48} className="text-yellow-400" />
                        </motion.div>
                      </div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Sticky Header with Ultra-Smooth Scroll Animation */}
        <motion.div
          className="fixed left-0 right-0 transition-all duration-500"
          animate={{
            top: isHeaderCompact ? "0px" : "64px",
            zIndex: isHeaderCompact ? 50 : 40,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{
            duration: 0.6,
            ease: [0.25, 0.46, 0.45, 0.94],
            type: "tween"
          }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-between"
            >
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => navigate('/customer/packages')}
                className="flex items-center gap-3 text-white hover:text-gray-300 transition-colors"
              >
                <ArrowLeft size={24} />
                <span className="font-medium">Back to Packages</span>
              </motion.button>

              <motion.div
                animate={{
                  opacity: isHeaderCompact ? 1 : 0,
                  scale: isHeaderCompact ? 1 : 0.8,
                }}
                transition={{ duration: 0.3 }}
                className="flex items-center gap-4"
              >
                <div className="flex items-center gap-3">
                  <Package className="text-white" size={24} />
                  <span className="text-white font-semibold text-lg">Package #{packageData.id.slice(-8)}</span>
                </div>

                <div className={`px-3 py-1 rounded-lg text-sm font-medium ${getStatusColor(packageData.status)}`}>
                  {packageData.status}
                </div>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </>
  );
};

export default PackageDetailsPage;
