import { Request, Response } from 'express';
import { Supplier } from '../models/Supplier';
import { validationResult } from 'express-validator';

export class SupplierController {
  // Get all active suppliers with optional filtering
  static async getSuppliers(req: Request, res: Response) {
    try {
      const { 
        category, 
        search, 
        lat, 
        lng, 
        radius = 10, 
        page = 1, 
        limit = 20,
        sortBy = 'rating',
        sortOrder = 'desc'
      } = req.query;

      // Build query
      const query: any = { isActive: true };

      // Filter by category
      if (category) {
        query.category = category;
      }

      // Search by name or product names
      if (search) {
        query.$text = { $search: search as string };
      }

      // Location-based filtering (if lat/lng provided)
      if (lat && lng) {
        const latitude = parseFloat(lat as string);
        const longitude = parseFloat(lng as string);
        const radiusInKm = parseFloat(radius as string);

        // Using MongoDB's geospatial query (approximate)
        query.lat = {
          $gte: latitude - (radiusInKm / 111), // 1 degree ≈ 111 km
          $lte: latitude + (radiusInKm / 111)
        };
        query.lng = {
          $gte: longitude - (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180))),
          $lte: longitude + (radiusInKm / (111 * Math.cos(latitude * Math.PI / 180)))
        };
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      // Sorting
      const sort: any = {};
      sort[sortBy as string] = sortOrder === 'desc' ? -1 : 1;

      const suppliers = await Supplier.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier by ID
  static async getSupplierById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const supplier = await Supplier.findOne({ id, isActive: true })
        .select('-__v');

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      res.json({
        success: true,
        data: supplier
      });
    } catch (error) {
      console.error('Error fetching supplier:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get suppliers by category
  static async getSuppliersByCategory(req: Request, res: Response) {
    try {
      const { category } = req.params;
      const { search, page = 1, limit = 20 } = req.query;

      const query: any = { category, isActive: true };

      if (search) {
        query.name = { $regex: search, $options: 'i' };
      }

      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;

      const suppliers = await Supplier.find(query)
        .sort({ rating: -1, name: 1 })
        .skip(skip)
        .limit(limitNum)
        .select('-__v');

      const total = await Supplier.countDocuments(query);

      res.json({
        success: true,
        data: suppliers,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          pages: Math.ceil(total / limitNum)
        }
      });
    } catch (error) {
      console.error('Error fetching suppliers by category:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch suppliers',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get supplier products
  static async getSupplierProducts(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { category, search, page = 1, limit = 20 } = req.query;

      const supplier = await Supplier.findOne({ id, isActive: true });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      let products = supplier.products.filter(p => p.isAvailable);

      // Filter by product category
      if (category && category !== 'All') {
        products = products.filter(p => p.category === category);
      }

      // Search in product names
      if (search) {
        const searchTerm = (search as string).toLowerCase();
        products = products.filter(p => 
          p.name.toLowerCase().includes(searchTerm)
        );
      }

      // Pagination
      const pageNum = parseInt(page as string);
      const limitNum = parseInt(limit as string);
      const skip = (pageNum - 1) * limitNum;
      const paginatedProducts = products.slice(skip, skip + limitNum);

      // Get unique categories from products
      const categories = ['All', ...new Set(supplier.products.map(p => p.category))];

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            deliveryTime: supplier.deliveryTime,
            openHours: supplier.openHours
          },
          products: paginatedProducts,
          categories,
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: products.length,
            pages: Math.ceil(products.length / limitNum)
          }
        }
      });
    } catch (error) {
      console.error('Error fetching supplier products:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch supplier products',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get product by ID from supplier
  static async getProductById(req: Request, res: Response): Promise<void> {
    try {
      const { supplierId, productId } = req.params;

      const supplier = await Supplier.findOne({ id: supplierId, isActive: true });

      if (!supplier) {
        res.status(404).json({
          success: false,
          message: 'Supplier not found'
        });
        return;
      }

      const product = supplier.products.find(p => p.id === productId && p.isAvailable);

      if (!product) {
        res.status(404).json({
          success: false,
          message: 'Product not found'
        });
        return;
      }

      res.json({
        success: true,
        data: {
          supplier: {
            id: supplier.id,
            name: supplier.name,
            rating: supplier.rating,
            deliveryTime: supplier.deliveryTime
          },
          product
        }
      });
    } catch (error) {
      console.error('Error fetching product:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch product',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
