import { <PERSON><PERSON>View } from 'react-native';
import { View, Text, Button, YStack } from 'tamagui';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useSendPackageStore } from './useSendPackageStore';

export function SendPackageConfirmation() {
  const router = useRouter();

  const { sendRequests } = useSendPackageStore();

  const lastRequest = sendRequests.at(-1);

  if (!lastRequest) {
    alert('No send request found. Redirecting to home.');
    router.replace('/home');
    return null;
  }

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
      {/* Header */}
      <View
        style={{
          paddingVertical: 40,
          paddingHorizontal: 24,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -20 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$10" fontWeight="800" color="white" textAlign="center">
            Package Request Sent!
          </Text>
          <Text fontSize="$5" color="white" textAlign="center" mt="$2">
            A driver will be assigned shortly
          </Text>
        </MotiView>
      </View>

      <YStack px="$4" py="$6" ai="center" gap="$5">
        {/* Success Icon */}
        <MotiView from={{ scale: 0.8, opacity: 0 }} animate={{ scale: 1, opacity: 1 }}>
          <View
            bg="$secondary"
            width={80}
            height={80}
            br="$10"
            jc="center"
            ai="center"
            shadowColor="#000"
            shadowOffset={{ width: 0, height: 4 }}
            shadowOpacity={0.1}
            shadowRadius={6}
          >
            <Ionicons name="checkmark" size={40} color="white" />
          </View>
        </MotiView>

        {/* Summary */}
        <View
          bg="$backgroundStrong"
          width="100%"
          maxWidth={380}
          p="$4"
          br="$6"
          borderWidth={1}
          borderColor="$colorTransparent"
        >
          <Text fontWeight="600" fontSize="$5">Pickup Location 📍:</Text>
          <Text mb="$3">{lastRequest.pickup?.address || 'N/A'}</Text>

          <Text fontWeight="600" fontSize="$5">Drop-off Location 📦:</Text>
          <Text mb="$3">{lastRequest.dropoff?.address || 'N/A'}</Text>

          <Text fontWeight="600" fontSize="$5">Receiver 👤:</Text>
          <Text mb="$3">{lastRequest.receiverName || 'N/A'} ({lastRequest.receiverPhone || 'N/A'})</Text>

          <Text fontWeight="600" fontSize="$5">Package Type 🗂️:</Text>
          <Text mb="$3">{lastRequest.packageType || 'N/A'}</Text>

          <Text fontWeight="600" fontSize="$5">Notes 📝:</Text>
          <Text>{lastRequest.notes || 'None'}</Text>
        </View>

        {/* Action Buttons */}
        <YStack gap="$3" width="100%" maxWidth={380}>
          <Button
            size="$5"
            bg="$primary"
            color="white"
            hoverStyle={{ bg: "$third" }}
            pressStyle={{ bg: "$third" }}
            onPress={() => router.push('/packages')}
          >
            Track My Package
          </Button>
          <Button
            size="$5"
            bg="$gray6"
            onPress={() => router.replace('/home')}
          >
            Back to Home
          </Button>
        </YStack>
      </YStack>
    </ScrollView>
  );
}
