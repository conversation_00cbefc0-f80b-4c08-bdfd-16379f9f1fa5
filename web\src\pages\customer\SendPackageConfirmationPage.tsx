import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { CheckCircle, Clock, MapPin, Phone, Package, User, Home, ArrowLeft, Truck, Star, Zap, ChevronRight, Copy, Share2, Bell } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import Logo from '../../components/common/Logo';

const SendPackageConfirmationPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showCelebration, setShowCelebration] = useState(true);
  const [copiedId, setCopiedId] = useState(false);
  
  // Get package data from location state or use mock data
  const packageData = location.state?.packageData || {
    id: `SP-${Date.now().toString().slice(-6)}`,
    fromAddress: 'Nablus, Palestine',
    toAddress: 'Ramallah, Palestine',
    senderName: '<PERSON> Same<PERSON>',
    senderPhone: '+970568406041',
    receiverName: 'Sara Ahmad',
    receiverPhone: '+970599123456',
    packageType: 'Documents',
    packageSize: 'small',
    packageWeight: '500g',
    price: 15,
    status: 'Pending',
    estimatedDelivery: '2-3 hours',
    createdAt: new Date().toISOString()
  };

  // Handle scroll for header animation
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Hide celebration after 3 seconds
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowCelebration(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  const handleCopyId = async () => {
    try {
      await navigator.clipboard.writeText(packageData.id);
      setCopiedId(true);
      setTimeout(() => setCopiedId(false), 2000);
    } catch (err) {
      console.error('Failed to copy package ID:', err);
    }
  };

  const handleShare = async () => {
    const shareData = {
      title: 'Package Tracking',
      text: `Track my package: ${packageData.id}`,
      url: `${window.location.origin}/customer/package-tracking?packageId=${packageData.id}`
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (err) {
        console.error('Error sharing:', err);
      }
    } else {
      // Fallback to copying URL
      try {
        await navigator.clipboard.writeText(shareData.url!);
        alert('Tracking link copied to clipboard!');
      } catch (err) {
        console.error('Failed to copy URL:', err);
      }
    }
  };

  const handleTrackPackage = () => {
    navigate(`/customer/package-tracking?packageId=${packageData.id}`);
  };

  const handleGoHome = () => {
    navigate('/customer/home');
  };

  const handleViewPackages = () => {
    navigate('/customer/packages');
  };

  const getSizeLabel = (size: string) => {
    const labels: Record<string, string> = {
      'small': 'Small (up to 30cm)',
      'medium': 'Medium (30-60cm)',
      'large': 'Large (60-100cm)',
      'extra-large': 'Extra Large (100cm+)'
    };
    return labels[size] || size;
  };

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-green-500/30 to-emerald-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute top-1/2 right-0 w-80 h-80 bg-gradient-to-br from-blue-500/30 to-cyan-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute bottom-0 left-1/2 w-72 h-72 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/20 rounded-full"
              animate={{
                y: [0, -100, 0],
                x: [0, Math.random() * 100 - 50, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>

        {/* Sticky Header with Scroll Animation - Only show when scrolling */}
        <motion.div
          className="fixed top-0 left-0 right-0 transition-all duration-500"
          animate={{
            zIndex: isHeaderCompact ? 50 : -10,
            opacity: isHeaderCompact ? 1 : 0,
            y: isHeaderCompact ? 0 : -100,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.4, ease: "easeInOut" }}
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              animate={{
                paddingTop: isHeaderCompact ? "1rem" : "2rem",
                paddingBottom: isHeaderCompact ? "1rem" : "2rem",
              }}
              transition={{ duration: 0.3 }}
              className="flex items-center justify-between"
            >
              <motion.button
                onClick={() => navigate(-1)}
                className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
                whileHover={{ x: -4 }}
                whileTap={{ scale: 0.95 }}
              >
                <ArrowLeft size={20} />
                <span className="font-medium">Back</span>
              </motion.button>

              <motion.div
                animate={{
                  scale: isHeaderCompact ? 0.8 : 1,
                }}
                transition={{ duration: 0.3 }}
              >
                <Logo size={isHeaderCompact ? "sm" : "md"} />
              </motion.div>

              <motion.button
                onClick={handleShare}
                className="flex items-center gap-2 text-white/80 hover:text-white transition-colors"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Share2 size={20} />
                <span className="font-medium">Share</span>
              </motion.button>
            </motion.div>
          </div>
        </motion.div>

        {/* Hero Section */}
        <div className="relative pt-32 pb-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            {/* Celebration Animation */}
            <AnimatePresence>
              {showCelebration && (
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  className="absolute inset-0 pointer-events-none"
                >
                  {[...Array(12)].map((_, i) => (
                    <motion.div
                      key={i}
                      className="absolute w-2 h-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full"
                      initial={{
                        x: "50%",
                        y: "50%",
                        scale: 0,
                      }}
                      animate={{
                        x: `${50 + (Math.cos((i * 30) * Math.PI / 180) * 200)}%`,
                        y: `${50 + (Math.sin((i * 30) * Math.PI / 180) * 200)}%`,
                        scale: [0, 1, 0],
                      }}
                      transition={{
                        duration: 2,
                        ease: "easeOut",
                        delay: i * 0.1,
                      }}
                    />
                  ))}
                </motion.div>
              )}
            </AnimatePresence>

            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative z-10"
            >
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="flex justify-center mb-8"
              >
                <div className="relative">
                  <motion.div
                    animate={{
                      boxShadow: [
                        "0 0 20px rgba(34, 197, 94, 0.3)",
                        "0 0 40px rgba(34, 197, 94, 0.6)",
                        "0 0 20px rgba(34, 197, 94, 0.3)",
                      ],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                    className="bg-white rounded-full p-8 shadow-2xl"
                  >
                    <CheckCircle className="w-20 h-20 text-green-500" />
                  </motion.div>

                  {/* Success pulse rings */}
                  <motion.div
                    animate={{
                      scale: [1, 2, 1],
                      opacity: [0.6, 0, 0.6],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeOut",
                    }}
                    className="absolute inset-0 border-4 border-green-400 rounded-full"
                  />
                  <motion.div
                    animate={{
                      scale: [1, 2.5, 1],
                      opacity: [0.4, 0, 0.4],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeOut",
                      delay: 0.5,
                    }}
                    className="absolute inset-0 border-2 border-green-300 rounded-full"
                  />
                </div>
              </motion.div>

              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.6 }}
                className="text-5xl md:text-6xl font-bold text-white mb-6"
              >
                <span className="bg-gradient-to-r from-green-400 via-emerald-500 to-green-600 bg-clip-text text-transparent">
                  Package Sent
                </span>
                <br />
                <span className="text-white">Successfully!</span>
              </motion.h1>

              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.8 }}
                className="text-xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed"
              >
                Your package is now in our system and will be picked up shortly.
                Track its journey in real-time and get notified at every step.
              </motion.p>

              {/* Package ID with copy functionality */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 1 }}
                className="inline-flex items-center gap-3 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl px-6 py-4 mb-8"
              >
                <Package className="w-5 h-5 text-white/80" />
                <span className="text-white/80 font-medium">Package ID:</span>
                <span className="text-white font-bold text-lg">{packageData.id}</span>
                <motion.button
                  onClick={handleCopyId}
                  className="ml-2 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {copiedId ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <Copy className="w-4 h-4 text-white/80" />
                  )}
                </motion.button>
                {copiedId && (
                  <motion.span
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: 10 }}
                    className="text-green-400 text-sm font-medium"
                  >
                    Copied!
                  </motion.span>
                )}
              </motion.div>
            </motion.div>
          </div>
        </div>

      {/* Content Section */}
      <div className="relative">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 pb-20">
          {/* Enhanced Progress Timeline */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
            className="mb-12"
          >
            <div className="bg-gradient-to-br from-white/15 to-white/5 backdrop-blur-sm border border-white/30 rounded-3xl p-8 shadow-2xl">
              <motion.h3
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 1.4 }}
                className="text-3xl font-bold text-white mb-2 text-center flex items-center justify-center gap-3"
              >
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                >
                  <Package className="w-8 h-8 text-emerald-400" />
                </motion.div>
                Package Journey
              </motion.h3>
              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.6, delay: 1.6 }}
                className="text-white/70 text-center mb-10"
              >
                Track your package's progress in real-time
              </motion.p>

              {/* Enhanced Timeline */}
              <div className="relative">
                {/* Timeline steps container */}
                <div className="flex items-start justify-between relative">
                  {/* Background progress line - positioned relative to circles */}
                  <div className="absolute top-8 h-2 bg-white/10 rounded-full" style={{ left: 'calc(12.5% + 2rem)', right: 'calc(12.5% + 2rem)' }}>
                    <motion.div
                      initial={{ width: "0%" }}
                      animate={{ width: "33.33%" }}
                      transition={{ duration: 3, delay: 1.8, ease: "easeInOut" }}
                      className="h-full bg-gradient-to-r from-emerald-400 via-green-500 to-emerald-600 rounded-full relative overflow-hidden"
                    >
                      {/* Animated shimmer effect */}
                      <motion.div
                        animate={{ x: ["-100%", "100%"] }}
                        transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
                      />
                    </motion.div>
                  </div>
                  {[
                    {
                      icon: CheckCircle,
                      label: "Package Submitted",
                      description: "Your package details have been confirmed",
                      time: "Just now",
                      active: true,
                      completed: true,
                      color: "emerald"
                    },
                    {
                      icon: Clock,
                      label: "Processing",
                      description: "Package is being prepared for pickup",
                      time: "In progress",
                      active: true,
                      completed: false,
                      color: "yellow"
                    },
                    {
                      icon: Truck,
                      label: "Driver Assigned",
                      description: "Driver will collect your package",
                      time: "Soon",
                      active: false,
                      completed: false,
                      color: "blue"
                    },
                    {
                      icon: MapPin,
                      label: "Delivered",
                      description: "Package reaches destination safely",
                      time: `${packageData.estimatedDelivery}`,
                      active: false,
                      completed: false,
                      color: "purple"
                    },
                  ].map((step, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 20, scale: 0.8 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{ duration: 0.6, delay: 1.8 + index * 0.3 }}
                      className="relative z-10 flex flex-col items-center flex-1"
                    >
                      {/* Step circle with enhanced styling */}
                      <motion.div
                        className={`relative w-16 h-16 rounded-full flex items-center justify-center border-4 shadow-lg ${
                          step.completed
                            ? step.color === 'emerald'
                              ? "bg-emerald-500 border-emerald-400 text-white shadow-emerald-500/50"
                              : step.color === 'yellow'
                              ? "bg-yellow-500 border-yellow-400 text-white shadow-yellow-500/50"
                              : step.color === 'blue'
                              ? "bg-blue-500 border-blue-400 text-white shadow-blue-500/50"
                              : "bg-purple-500 border-purple-400 text-white shadow-purple-500/50"
                            : step.active
                            ? step.color === 'emerald'
                              ? "bg-emerald-500 border-emerald-400 text-white shadow-emerald-500/50"
                              : step.color === 'yellow'
                              ? "bg-yellow-500 border-yellow-400 text-white shadow-yellow-500/50"
                              : step.color === 'blue'
                              ? "bg-blue-500 border-blue-400 text-white shadow-blue-500/50"
                              : "bg-purple-500 border-purple-400 text-white shadow-purple-500/50"
                            : step.color === 'blue'
                            ? "bg-gradient-to-br from-blue-500/30 to-blue-600/20 border-blue-400/40 text-blue-100 backdrop-blur-sm"
                            : "bg-gradient-to-br from-purple-500/30 to-purple-600/20 border-purple-400/40 text-purple-100 backdrop-blur-sm"
                        }`}
                        animate={step.active && !step.completed ? {
                          scale: [1, 1.1, 1],
                          boxShadow: step.color === 'yellow' ? [
                            "0 0 20px rgba(234, 179, 8, 0.3)",
                            "0 0 40px rgba(234, 179, 8, 0.6)",
                            "0 0 20px rgba(234, 179, 8, 0.3)",
                          ] : step.color === 'blue' ? [
                            "0 0 20px rgba(59, 130, 246, 0.3)",
                            "0 0 40px rgba(59, 130, 246, 0.6)",
                            "0 0 20px rgba(59, 130, 246, 0.3)",
                          ] : [
                            "0 0 20px rgba(16, 185, 129, 0.3)",
                            "0 0 40px rgba(16, 185, 129, 0.6)",
                            "0 0 20px rgba(16, 185, 129, 0.3)",
                          ],
                        } : step.completed ? {
                          boxShadow: [
                            "0 0 20px rgba(16, 185, 129, 0.4)",
                            "0 0 30px rgba(16, 185, 129, 0.6)",
                            "0 0 20px rgba(16, 185, 129, 0.4)",
                          ],
                        } : {
                          // Subtle breathing animation for pending steps
                          scale: [1, 1.02, 1],
                          opacity: [0.8, 1, 0.8],
                          boxShadow: step.color === 'blue' ? [
                            "0 0 15px rgba(59, 130, 246, 0.2)",
                            "0 0 25px rgba(59, 130, 246, 0.4)",
                            "0 0 15px rgba(59, 130, 246, 0.2)",
                          ] : [
                            "0 0 15px rgba(147, 51, 234, 0.2)",
                            "0 0 25px rgba(147, 51, 234, 0.4)",
                            "0 0 15px rgba(147, 51, 234, 0.2)",
                          ],
                        }}
                        transition={{
                          duration: step.active && !step.completed ? 2 : 4,
                          repeat: Infinity,
                          ease: "easeInOut",
                        }}
                      >
                        <step.icon size={24} />

                        {/* Completion checkmark overlay */}
                        {step.completed && (
                          <motion.div
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ duration: 0.5, delay: 2 + index * 0.3 }}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full flex items-center justify-center border-2 border-white"
                          >
                            <CheckCircle size={14} className="text-white" />
                          </motion.div>
                        )}

                        {/* Active pulse rings */}
                        {step.active && !step.completed && (
                          <>
                            <motion.div
                              animate={{
                                scale: [1, 2, 1],
                                opacity: [0.6, 0, 0.6],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeOut",
                              }}
                              className="absolute inset-0 border-4 border-yellow-400 rounded-full"
                            />
                            <motion.div
                              animate={{
                                scale: [1, 2.5, 1],
                                opacity: [0.4, 0, 0.4],
                              }}
                              transition={{
                                duration: 2,
                                repeat: Infinity,
                                ease: "easeOut",
                                delay: 0.5,
                              }}
                              className="absolute inset-0 border-2 border-yellow-300 rounded-full"
                            />
                          </>
                        )}

                        {/* Pending step indicator */}
                        {!step.active && !step.completed && (
                          <motion.div
                            animate={{
                              rotate: [0, 360],
                            }}
                            transition={{
                              duration: 8,
                              repeat: Infinity,
                              ease: "linear",
                            }}
                            className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-white/30 to-white/10 rounded-full border border-white/40"
                          >
                            <div className="w-1 h-1 bg-white/60 rounded-full absolute top-0.5 left-0.5"></div>
                          </motion.div>
                        )}
                      </motion.div>

                      {/* Step content */}
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.6, delay: 2.2 + index * 0.3 }}
                        className="mt-4 text-center"
                      >
                        <h4 className={`font-bold text-sm mb-1 ${
                          step.active ? "text-white" : "text-white/80"
                        }`}>
                          {step.label}
                        </h4>
                        <p className={`text-xs leading-relaxed mb-2 ${
                          step.active ? "text-white/80" : "text-white/70"
                        }`}>
                          {step.description}
                        </p>
                        <span className={`text-xs font-medium px-2 py-1 rounded-full ${
                          step.completed
                            ? "bg-emerald-500/20 text-emerald-300 border border-emerald-500/30"
                            : step.active
                            ? "bg-yellow-500/20 text-yellow-300 border border-yellow-500/30"
                            : step.color === 'blue'
                            ? "bg-blue-500/20 text-blue-300 border border-blue-500/30"
                            : "bg-purple-500/20 text-purple-300 border border-purple-500/30"
                        }`}>
                          {step.time}
                        </span>
                      </motion.div>
                    </motion.div>
                  ))}
                </div>

                {/* Estimated completion time */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 3.5 }}
                  className="mt-8 text-center"
                >
                  <div className="inline-flex items-center gap-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3">
                    <Clock className="w-4 h-4 text-emerald-400" />
                    <span className="text-white/90 font-medium">
                      Estimated completion: {packageData.estimatedDelivery}
                    </span>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Package Details Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.4 }}
            className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl overflow-hidden mb-8"
          >
            {/* Card Header */}
            <div className="bg-gradient-to-r from-white/20 to-white/10 p-6 border-b border-white/20">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-white flex items-center gap-3">
                  <Package className="w-6 h-6 text-emerald-400" />
                  Package Details
                </h2>
                <motion.div
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                  className="px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                >
                  <span className="text-white font-bold text-sm flex items-center gap-2">
                    <Zap className="w-4 h-4" />
                    {packageData.status}
                  </span>
                </motion.div>
              </div>
            </div>

            <div className="p-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Left Column - Route & Timing */}
                <div className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 1.6 }}
                    className="bg-white/5 rounded-xl p-6 border border-white/10"
                  >
                    <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <MapPin className="w-5 h-5 text-emerald-400" />
                      Route Information
                    </h3>

                    <div className="space-y-4">
                      <div className="flex items-start gap-4">
                        <div className="w-3 h-3 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="font-semibold text-white/90">From</p>
                          <p className="text-white/70">{packageData.fromAddress}</p>
                        </div>
                      </div>

                      <div className="ml-1.5 border-l-2 border-dashed border-white/30 h-8"></div>

                      <div className="flex items-start gap-4">
                        <div className="w-3 h-3 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="font-semibold text-white/90">To</p>
                          <p className="text-white/70">{packageData.toAddress}</p>
                        </div>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 1.8 }}
                    className="bg-white/5 rounded-xl p-6 border border-white/10"
                  >
                    <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <Clock className="w-5 h-5 text-blue-400" />
                      Timing & Status
                    </h3>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-white/70">Estimated Delivery</span>
                        <span className="text-white font-semibold">{packageData.estimatedDelivery}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/70">Created At</span>
                        <span className="text-white font-semibold">
                          {new Date(packageData.createdAt).toLocaleTimeString()}
                        </span>
                      </div>
                    </div>
                  </motion.div>
                </div>

                {/* Right Column - Contact & Package Info */}
                <div className="space-y-6">
                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 1.6 }}
                    className="bg-white/5 rounded-xl p-6 border border-white/10"
                  >
                    <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <User className="w-5 h-5 text-purple-400" />
                      Contact Information
                    </h3>

                    <div className="space-y-4">
                      <div className="bg-white/5 rounded-lg p-4">
                        <p className="font-semibold text-white/90 mb-1">Sender</p>
                        <p className="text-white/70">{packageData.senderName}</p>
                        <p className="text-white/60 text-sm flex items-center gap-2 mt-1">
                          <Phone className="w-3 h-3" />
                          {packageData.senderPhone}
                        </p>
                      </div>

                      <div className="bg-white/5 rounded-lg p-4">
                        <p className="font-semibold text-white/90 mb-1">Receiver</p>
                        <p className="text-white/70">{packageData.receiverName}</p>
                        <p className="text-white/60 text-sm flex items-center gap-2 mt-1">
                          <Phone className="w-3 h-3" />
                          {packageData.receiverPhone}
                        </p>
                      </div>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: 1.8 }}
                    className="bg-white/5 rounded-xl p-6 border border-white/10"
                  >
                    <h3 className="text-lg font-bold text-white mb-4 flex items-center gap-2">
                      <Package className="w-5 h-5 text-orange-400" />
                      Package Specifications
                    </h3>

                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-white/70">Type</span>
                        <span className="text-white font-semibold">{packageData.packageType}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/70">Size</span>
                        <span className="text-white font-semibold">{getSizeLabel(packageData.packageSize)}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-white/70">Weight</span>
                        <span className="text-white font-semibold">{packageData.packageWeight}</span>
                      </div>
                      <div className="border-t border-white/20 pt-3 mt-4">
                        <div className="flex justify-between items-center">
                          <span className="text-white/70">Delivery Fee</span>
                          <motion.span
                            animate={{
                              scale: [1, 1.05, 1],
                            }}
                            transition={{
                              duration: 2,
                              repeat: Infinity,
                              ease: "easeInOut",
                            }}
                            className="text-emerald-400 font-bold text-lg"
                          >
                            ₪{packageData.price}
                          </motion.span>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Enhanced Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6"
          >
            <motion.button
              onClick={handleTrackPackage}
              className="group relative bg-gradient-to-r from-emerald-500 to-green-600 text-white py-4 px-8 rounded-xl font-bold text-lg shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-emerald-600 to-green-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative flex items-center justify-center gap-3">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                >
                  <Clock className="w-6 h-6" />
                </motion.div>
                Track Package
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </motion.button>

            <motion.button
              onClick={handleViewPackages}
              className="group relative bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white py-4 px-8 rounded-xl font-bold text-lg hover:bg-white/20 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-center gap-3">
                <Package className="w-6 h-6" />
                View All Packages
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </motion.button>

            <motion.button
              onClick={handleGoHome}
              className="group relative bg-white/10 backdrop-blur-sm border-2 border-white/30 text-white py-4 px-8 rounded-xl font-bold text-lg hover:bg-white/20 transition-all duration-300"
              whileHover={{ scale: 1.02, y: -2 }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center justify-center gap-3">
                <Home className="w-6 h-6" />
                Back to Home
                <ChevronRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </div>
            </motion.button>
          </motion.div>

          {/* Enhanced What's Next Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2.2 }}
            className="mt-12"
          >
            <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-2xl p-8">
              <motion.h3
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 2.4 }}
                className="text-2xl font-bold text-white mb-6 text-center flex items-center justify-center gap-3"
              >
                <Bell className="w-6 h-6 text-yellow-400" />
                What happens next?
              </motion.h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    icon: Clock,
                    title: "Processing",
                    description: "Your package is being processed and prepared for pickup",
                    color: "text-blue-400"
                  },
                  {
                    icon: Truck,
                    title: "Driver Assignment",
                    description: "A nearby driver will be assigned to collect your package",
                    color: "text-purple-400"
                  },
                  {
                    icon: Bell,
                    title: "Real-time Updates",
                    description: "You'll receive notifications at every step of the journey",
                    color: "text-yellow-400"
                  },
                  {
                    icon: MapPin,
                    title: "Out for Delivery",
                    description: "Receiver will be notified when package is out for delivery",
                    color: "text-green-400"
                  },
                  {
                    icon: CheckCircle,
                    title: "Delivery Confirmation",
                    description: "Both parties will receive delivery confirmation",
                    color: "text-emerald-400"
                  },
                  {
                    icon: Star,
                    title: "Rate Experience",
                    description: "Share your feedback to help us improve our service",
                    color: "text-orange-400"
                  }
                ].map((step, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 2.4 + index * 0.1 }}
                    className="bg-white/5 rounded-xl p-6 border border-white/10 hover:bg-white/10 transition-colors"
                  >
                    <div className={`w-12 h-12 rounded-full bg-white/10 flex items-center justify-center mb-4 ${step.color}`}>
                      <step.icon size={24} />
                    </div>
                    <h4 className="font-bold text-white mb-2">{step.title}</h4>
                    <p className="text-white/70 text-sm leading-relaxed">{step.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Notification Preferences */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 2.6 }}
            className="mt-8 text-center"
          >
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 backdrop-blur-sm border border-white/20 rounded-xl p-6">
              <p className="text-white/80 mb-4">
                Want to stay updated? We'll send you notifications via SMS and email.
              </p>
              <div className="flex items-center justify-center gap-4 text-sm text-white/60">
                <span className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  SMS Notifications: Enabled
                </span>
                <span className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  Email Updates: Enabled
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
      </div>
    </>
  );
};

export default SendPackageConfirmationPage;
