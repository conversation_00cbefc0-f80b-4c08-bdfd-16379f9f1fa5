import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Localization from 'expo-localization';
import { I18nManager } from 'react-native';
import i18n from '../i18n';

export type Language = 'en' | 'ar';

interface LanguageState {
  currentLanguage: Language;
  isRTL: boolean;
  isLoading: boolean;
  setLanguage: (language: Language) => Promise<void>;
  initializeLanguage: () => Promise<void>;
  toggleLanguage: () => Promise<void>;
}

const LANGUAGE_STORAGE_KEY = '@wasel_language';

// Get device language with fallback
const getDeviceLanguage = (): Language => {
  try {
    const locale = Localization.locale;
    if (!locale || typeof locale !== 'string') {
      return 'en'; // Default fallback
    }
    const deviceLanguage = locale.split('-')[0];
    return deviceLanguage === 'ar' ? 'ar' : 'en';
  } catch (error) {
    console.warn('Error getting device language:', error);
    return 'en'; // Default fallback
  }
};

export const useLanguageStore = create<LanguageState>((set, get) => ({
  currentLanguage: 'en',
  isRTL: false,
  isLoading: true,

  setLanguage: async (language: Language) => {
    try {
      console.log(`🌐 Setting language to: ${language}`);

      // Update i18n language first and wait for it to complete
      await i18n.changeLanguage(language);
      console.log(`🌐 i18n language changed to: ${i18n.language}`);

      // Save to AsyncStorage
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);

      // Update RTL setting
      const isRTL = language === 'ar';

      // Force RTL layout update for React Native
      if (I18nManager.isRTL !== isRTL) {
        I18nManager.allowRTL(isRTL);
        I18nManager.forceRTL(isRTL);
      }

      // Update state
      set({
        currentLanguage: language,
        isRTL,
      });

      console.log(`🌐 Language change completed: ${language}, RTL: ${isRTL}`);
    } catch (error) {
      console.error('Error saving language:', error);
    }
  },

  initializeLanguage: async () => {
    try {
      set({ isLoading: true });

      // Try to get saved language from storage
      const savedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);

      let language: Language;
      if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'ar')) {
        language = savedLanguage as Language;
      } else {
        // Use device language as fallback
        language = getDeviceLanguage();
      }

      // Initialize i18n with the selected language
      await i18n.changeLanguage(language);
      console.log(`🌐 i18n initialized with language: ${i18n.language}`);

      // Set up RTL
      const isRTL = language === 'ar';
      I18nManager.allowRTL(isRTL);
      I18nManager.forceRTL(isRTL);

      // Update state
      set({
        currentLanguage: language,
        isRTL,
        isLoading: false,
      });

      console.log(`🌐 Language initialized: ${language}, RTL: ${isRTL}`);
    } catch (error) {
      console.error('Error initializing language:', error);
      // Fallback to English
      await i18n.changeLanguage('en');
      set({
        currentLanguage: 'en',
        isRTL: false,
        isLoading: false,
      });
    }
  },

  toggleLanguage: async () => {
    const { currentLanguage, setLanguage } = get();
    const newLanguage: Language = currentLanguage === 'en' ? 'ar' : 'en';
    await setLanguage(newLanguage);
  },
}));
 

