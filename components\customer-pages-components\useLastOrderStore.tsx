import { create } from 'zustand';
import type { CartItem } from './CartStore';

type Location = {
  lat: number;
  lng: number;
  address: string;
};

export type LastOrder = {
  id: string
  items: CartItem[]
  total: number
  estimatedTime: string
  address: Location | null
  phone: string
  paymentMethod: 'cash' | 'card'
  placedAt: string
  orderStatus: 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'
  driverName: String
};

type AddressStore = LastOrder & {
  setAddress: (loc: Location) => void
}

export const useSetOrderAddress = create<AddressStore>((set) => ({
  id: '',
  items: [],
  total: 0,
  estimatedTime: '',
  address: null,
  phone: '',
  paymentMethod: 'cash',
  placedAt: '',
  orderStatus: 'Pending',
  driverName: '',
  setAddress: (address) => set({ address }),
}));

type LastOrderState = {
  lastOrderGroup: LastOrder[] // instead of a single order
  setLastOrderGroup: (orders: LastOrder[]) => void
  clearLastOrderGroup: () => void
};

export const useLastOrderStore = create<LastOrderState>((set) => ({
  lastOrderGroup: [],
  setLastOrderGroup: (orders) => set({ lastOrderGroup: orders }),
  clearLastOrderGroup: () => set({ lastOrderGroup: [] }),
}));