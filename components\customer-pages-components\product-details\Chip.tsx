import { Button, Text } from 'tamagui'

type ChipProps = {
  label: string
  selected?: boolean
  onPress?: () => void
  iconAfter?: React.ReactNode
}

export const Chip = ({ label, selected = false, onPress, iconAfter }: ChipProps) => {
  return (
    <Button
      size="$2"
      br="$8"
      px="$3"
      bg={selected ? '$primary' : '$gray2'}
      boc={selected ? '$primary' : '$gray5'}
      bw={1}
      onPress={onPress}
    >
      <Text color={selected ? 'white' : '$gray10'}>{label}</Text>
      {iconAfter && (
        <Text color={selected ? 'white' : '$gray9'} ml="$1">
          {iconAfter}
        </Text>
      )}
    </Button>
  )
}
