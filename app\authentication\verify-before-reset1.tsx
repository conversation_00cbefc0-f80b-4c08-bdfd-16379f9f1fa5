import React, { useState } from 'react';
import { Stack, router } from 'expo-router';
import { Alert } from 'react-native';
import { YStack, XStack, H2, Text, Button, Input, Label, Card, Separator, Image } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { Container } from '~/components/Container';
import { apiService } from '~/services/api';
import { useTranslation } from 'react-i18next';

export default function ForgotPasswordStep1() {
  const { t } = useTranslation();
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'code'>('email'); // Track current step
  const [userEmail, setUserEmail] = useState(''); // Store verified email

  const handleSendCode = async () => {
    if (!email.trim()) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.enterEmail', { defaultValue: 'Please enter your email address' }));
      return;
    }

    if (!email.includes('@')) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('validation.invalidEmail', { defaultValue: 'Please enter a valid email address' }));
      return;
    }

    setLoading(true);
    try {
      const response = await apiService.forgotPassword(email.trim().toLowerCase());

      if (response.success) {
        setUserEmail(email.trim().toLowerCase());
        setStep('code');
        Alert.alert(
          t('auth.sendCode', { defaultValue: 'Code Sent' }),
          t('auth.codeSentMessage', { defaultValue: `A 6-digit verification code has been sent to ${email}. Please check your email.` }),
        );
      } else {
        Alert.alert(t('common.error', { defaultValue: 'Error' }), response.message || t('auth.emailNotFound', { defaultValue: 'No account found with this email address.' }));
      }
    } catch (error) {
      console.error('Forgot password error:', error);
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('errors.somethingWentWrong', { defaultValue: 'Something went wrong. Please try again.' }));
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyCode = async () => {
    if (!verificationCode.trim() || verificationCode.length !== 6) {
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('auth.enterVerificationCode', { defaultValue: 'Please enter the 6-digit verification code' }));
      return;
    }

    setLoading(true);
    try {
      const response = await apiService.verifyResetCode(userEmail, verificationCode.trim());
      if (response.success) {
        // Navigate to reset password page with email and code
        router.push({
          pathname: '/authentication/reset-password',
          params: { email: userEmail, code: verificationCode.trim() }
        });
      } else {
        Alert.alert(t('common.error', { defaultValue: 'Error' }), response.message || t('auth.invalidCode', { defaultValue: 'Invalid or expired verification code.' }));
      }
    } catch (error) {
      console.error('Verify code error:', error);
      Alert.alert(t('common.error', { defaultValue: 'Error' }), t('errors.somethingWentWrong', { defaultValue: 'Something went wrong. Please try again.' }));
    } finally {
      setLoading(false);
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setVerificationCode('');
    setUserEmail('');
  };

  return (
    <>
      <Stack.Screen options={{
        title: step === 'email' ? t('auth.forgotPassword', { defaultValue: 'Forgot Password' }) : t('auth.verifyCode', { defaultValue: 'Verify Code' }),
        headerBackTitle: t('common.back', { defaultValue: 'Back' })
      }} />
      <Container alignSelf='center' alignItems="center" justifyContent="center" padding="$6" paddingTop={85} paddingBottom={85} style={{ width: '90%' }}>
        <Card padding="$6" width="100%" maxWidth={400} backgroundColor="$background">
          <YStack space="$4" alignItems="center">
            <Image
              source={require('../../assets/forgot.png')}
              style={{ width: 128, height: 128 }}
            />

            {step === 'email' ? (
              <>
                <YStack space="$2" alignItems="center">
                  <H2 color="$color" textAlign="center">{t('auth.forgotPasswordTitle', { defaultValue: 'Forgot Password?' })}</H2>
                  <Text color="$gray10" textAlign="center" fontSize="$4">
                    {t('auth.forgotPasswordDescription', { defaultValue: 'Enter your email address and we\'ll send you a verification code to reset your password.' })}
                  </Text>
                </YStack>

                <Separator />

                <YStack space="$3" width="100%">
                  <YStack space="$2">
                    <Label htmlFor="email">{t('auth.emailAddress', { defaultValue: 'Email Address' })}</Label>
                    <XStack space="$2" alignItems="center">
                      <Ionicons name="mail" size={20} color="#666" />
                      <Input
                        id="email"
                        flex={1}
                        value={email}
                        onChangeText={setEmail}
                        placeholder={t('auth.enterEmail', { defaultValue: 'Enter your email address' })}
                        keyboardType="email-address"
                        autoCapitalize="none"
                        autoCorrect={false}
                        editable={!loading}
                      />
                    </XStack>
                  </YStack>

                  <Button
                    backgroundColor="#67B329"
                    color="white"
                    onPress={handleSendCode}
                    disabled={loading}
                    opacity={loading ? 0.7 : 1}
                  >
                    {loading ? t('common.sending', { defaultValue: 'Sending...' }) : t('auth.sendCode', { defaultValue: 'Send Verification Code' })}
                  </Button>

                  <Button
                    variant="outlined"
                    onPress={() => router.push('/authentication/login')}
                    disabled={loading}
                  >
                    {t('auth.backToLogin', { defaultValue: 'Back to Login' })}
                  </Button>
                </YStack>
              </>
            ) : (
              <>
                <YStack space="$2" alignItems="center">
                  <H2 color="$color" textAlign="center">{t('auth.enterVerificationCodeTitle', { defaultValue: 'Enter Verification Code' })}</H2>
                  <Text color="$gray10" textAlign="center" fontSize="$4">
                    {t('auth.codeSentTo', { defaultValue: `We've sent a 6-digit code to ${userEmail}` })}
                  </Text>
                </YStack>

                <Separator />

                <YStack space="$3" width="100%">
                  <YStack space="$2">
                    <Label htmlFor="code">{t('auth.verificationCode', { defaultValue: 'Verification Code' })}</Label>
                    <XStack space="$2" alignItems="center">
                      <Ionicons name="shield-checkmark" size={20} color="#666" />
                      <Input
                        id="code"
                        flex={1}
                        value={verificationCode}
                        onChangeText={setVerificationCode}
                        placeholder={t('auth.enterSixDigitCode', { defaultValue: 'Enter 6-digit code' })}
                        keyboardType="numeric"
                        maxLength={6}
                        editable={!loading}
                        textAlign="center"
                        fontSize="$6"
                        letterSpacing={4}
                      />
                    </XStack>
                  </YStack>

                  <Button
                    backgroundColor="#67B329"
                    color="white"
                    onPress={handleVerifyCode}
                    disabled={loading || verificationCode.length !== 6}
                    opacity={loading || verificationCode.length !== 6 ? 0.7 : 1}
                  >
                    {loading ? t('common.verifying', { defaultValue: 'Verifying...' }) : t('auth.verifyCode', { defaultValue: 'Verify Code' })}
                  </Button>

                  <Button
                    variant="outlined"
                    onPress={handleBackToEmail}
                    disabled={loading}
                  >
                    {t('auth.changeEmail', { defaultValue: 'Change Email' })}
                  </Button>

                  <Button
                    variant="outlined"
                    onPress={() => router.push('/authentication/login')}
                    disabled={loading}
                  >
                    {t('auth.backToLogin', { defaultValue: 'Back to Login' })}
                  </Button>
                </YStack>
              </>
            )}
          </YStack>
        </Card>
      </Container>
    </>
  );
}