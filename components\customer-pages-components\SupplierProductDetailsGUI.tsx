import { Stack, useLocalSearchParams, useRouter } from 'expo-router';
import { RestaurantProductDetails } from './product-details/RestaurantProductDetails';
import { ClothingProductDetails } from './product-details/ClothingProductDetails';

type Addition = { id: string; name: string; price: number }

type SupplierDetailsGUIProps = {
    category: string;
}

export function SupplierProductDetailsGUI({ category }: SupplierDetailsGUIProps) {
  const router = useRouter();
  const params = useLocalSearchParams();
  const product: {
    id: string;
    name: string;
    image: string;
    price: number;
    category: string;
    restaurantOptions?: {
      additions?: Addition[];
      without?: string[];
      sides?: Addition[];
    };
    clothingOptions?: {
      sizes: string[];
      colors: string[];
      gallery: string[];
    };
  } | undefined = params.product ? JSON.parse(params.product as string) : undefined;

  const title = product?.name ?? 'Item';

  const supplierId = Array.isArray(params.supplierId) ? params.supplierId[0] : params.supplierId;
  const supplierName = Array.isArray(params.supplierName) ? params.supplierName[0] : params.supplierName;

  return (
    <>
      <Stack.Screen options={{ title, headerShown: true }} />
      {product && (category === 'restaurants' ? (
        <RestaurantProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      ) : (
        <ClothingProductDetails product={product} supplierId={supplierId} supplierName={supplierName} />
      ))}
    </>
  )
}