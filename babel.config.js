module.exports = function (api) {
  api.cache(true);
  let plugins = [];

  // Disable Tamagui babel plugin in development to avoid configuration issues
  if (process.env.NODE_ENV === 'production') {
    plugins.push([
      '@tamagui/babel-plugin',
      {
        components: ['tamagui'],
        config: './tamagui.config.ts',
        logTimings: true,
      }
    ]);
  }

  plugins.push('react-native-reanimated/plugin');

  return {
    presets: ['babel-preset-expo'],
    plugins,
  };
};
