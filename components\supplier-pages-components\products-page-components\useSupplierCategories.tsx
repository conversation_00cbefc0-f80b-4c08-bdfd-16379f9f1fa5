import { create } from 'zustand';

type CategoryStore = {
  categories: string[];
  selectedCategory: string | null;
  addCategory: (cat: string) => void;
  deleteCategory: (cat: string) => void;
  selectCategory: (cat: string | null) => void;
  renameCategory: (oldCat: string, newCat: string) => void;
}

export const useSupplierCategories = create<CategoryStore>((set) => ({
  categories: [],
  selectedCategory: null,
  addCategory: (cat) =>
    set((s) => ({ categories: [...s.categories, cat.trim()] })),
  deleteCategory: (cat) =>
    set((s) => ({
      categories: s.categories.filter((c) => c !== cat),
      selectedCategory: s.selectedCategory === cat ? null : s.selectedCategory,
    })),
  selectCategory: (cat) => set(() => ({ selectedCategory: cat })),
  renameCategory: (oldCat, newCat) =>
    set((s) => ({
      categories: s.categories.map((c) =>
        c === oldCat ? newCat.trim() : c
      ),
    })),
}));
