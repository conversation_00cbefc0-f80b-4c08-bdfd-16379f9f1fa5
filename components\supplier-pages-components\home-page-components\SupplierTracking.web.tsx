import { useEffect, useState } from 'react';
import { ScrollView, Dimensions, Linking } from 'react-native';
import { useLocalSearchParams, Stack } from 'expo-router';
import { <PERSON>, YStack, Card, H4, H6, <PERSON><PERSON><PERSON>ck, <PERSON><PERSON>, Separator, <PERSON>ton, View } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useMyOrdersStore } from '~/components/customer-pages-components/orders-page-components/useMyOrdersStore';
// Use react-native-web-maps directly for web
import MapView, { <PERSON><PERSON>, Polyline } from 'react-native-web-maps';

export default function SupplierTracking() {
  const { orderId } = useLocalSearchParams<{ orderId: string }>();
  const { width, height } = Dimensions.get('window');

  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === orderId));

  const [simulatedDriver, setSimulatedDriver] = useState(order?.driverLocation ?? null);
  const [estimatedTime, setEstimatedTime] = useState(12);
  const [isLiveTracking, setIsLiveTracking] = useState(true);

  const dropoff = order?.address?.lng && order?.address?.lat
    ? [order.address.lng, order.address.lat]
    : [35.2544, 32.2211]; // Nablus, Palestine

  useEffect(() => {
    if (!order?.driverLocation) return;

    const interval = setInterval(() => {
      setSimulatedDriver((prev) => {
        if (!prev) return null;

        // Calculate distance to destination
        const distance = Math.sqrt(
          Math.pow(dropoff[0] - prev.lng, 2) + Math.pow(dropoff[1] - prev.lat, 2)
        );

        // If very close to destination, stop moving
        if (distance < 0.0001) {
          setIsLiveTracking(false);
          setEstimatedTime(0);
          return prev;
        }

        // Move towards destination with some randomness
        const moveSpeed = 0.00008;
        const randomFactor = 0.00002;

        return {
          lng: prev.lng + (Math.random() - 0.5) * randomFactor + moveSpeed,
          lat: prev.lat + (Math.random() - 0.5) * randomFactor + moveSpeed * 0.6,
          address: 'Moving towards destination...'
        };
      });

      // Update estimated time
      setEstimatedTime(prev => Math.max(0, prev - 0.5));
    }, 3000);

    return () => clearInterval(interval);
  }, [order?.driverLocation, dropoff]);



  if (!order) {
    return (
      <View flex={1} justifyContent="center" alignItems="center">
        <Text>Order not found</Text>
      </View>
    );
  }

  if (!simulatedDriver) {
    return (
      <View flex={1} ai="center" jc="center" bg="$gray1">
        <YStack ai="center" gap="$4" p="$6">
          <Spinner size="large" color="$primary" />
          <H4 color="$gray11">Initializing Tracking...</H4>
          <Text color="$gray9" textAlign="center">
            Setting up real-time location tracking for your order.
          </Text>
        </YStack>
      </View>
    );
  }

  const getMapRegion = () => {
    if (!order?.address) {
      return {
        latitude: 32.2211,
        longitude: 35.2544,
        latitudeDelta: 0.01,
        longitudeDelta: 0.01,
      };
    }

    return {
      latitude: order.address.lat,
      longitude: order.address.lng,
      latitudeDelta: 0.01,
      longitudeDelta: 0.01,
    };
  };

  const getPolylineCoordinates = () => {
    if (!order?.address || !simulatedDriver) return [];

    return [
      { latitude: 32.2211, longitude: 35.2544 }, // Restaurant location
      { latitude: simulatedDriver.lat, longitude: simulatedDriver.lng },
      { latitude: order.address.lat, longitude: order.address.lng },
    ];
  };

  return (
    <>
      <Stack.Screen 
        options={{ 
          title: `Order #${order.id.slice(-6)}`,
          headerShown: true 
        }} 
      />
      
      <ScrollView style={{ flex: 1 }}>
        {/* Map Section */}
        <View style={{ height: height * 0.4 }}>
          <MapView
            style={{ width, height: height * 0.4 }}
            region={getMapRegion()}
            showsUserLocation={true}
          >
            {/* Restaurant Marker */}
            <Marker
              coordinate={{
                latitude: 32.2211,
                longitude: 35.2544,
              }}
              pinColor="orange"
              title="Restaurant"
            />
            
            {/* Delivery Marker */}
            {order?.address && (
              <Marker
                coordinate={{
                  latitude: order.address.lat,
                  longitude: order.address.lng,
                }}
                pinColor="red"
                title="Delivery Location"
              />
            )}

            {/* Driver Marker */}
            {simulatedDriver && (
              <Marker
                coordinate={{
                  latitude: simulatedDriver.lat,
                  longitude: simulatedDriver.lng,
                }}
                pinColor="blue"
                title="Driver Location"
              />
            )}
            
            {/* Route Polyline */}
            <Polyline
              coordinates={getPolylineCoordinates()}
              strokeColor="#007AFF"
              strokeWidth={3}
            />
          </MapView>
        </View>

        {/* Order Status */}
        <YStack p="$4" gap="$4">
          <Card p="$4" bg="$blue2" br="$4">
            <YStack gap="$2">
              <H4 color="$blue11">Order in Progress</H4>
              <Text color="$blue10">
                Estimated delivery: {estimatedTime} minutes
              </Text>
            </YStack>
          </Card>

          {/* Driver Info */}
          <Card p="$4" br="$4">
            <YStack gap="$3">
              <H6>Driver Information</H6>
              <XStack gap="$3" alignItems="center">
                <View w={50} h={50} br={25} bg="$gray5" />
                <YStack flex={1}>
                  <Text fontSize="$4" fontWeight="500">{order.driverName}</Text>
                  <Text fontSize="$3" color="$gray10">★ 4.8 • Toyota Corolla</Text>
                </YStack>
                <Button
                  size="$3"
                  bg="$green9"
                  color="white"
                  onPress={() => Linking.openURL(`tel:${order.driverPhone}`)}
                  icon={<Ionicons name="call" size={16} color="white" />}
                >
                  Call
                </Button>
              </XStack>
            </YStack>
          </Card>

          {/* Order Summary */}
          <Card p="$4" br="$4">
            <YStack gap="$3">
              <H6>Order Summary</H6>
              {order.items.map((item, index) => (
                <XStack key={index} justifyContent="space-between">
                  <Text>{item.qty}x {item.product.name}</Text>
                  <Text fontWeight="500">₪{item.finalPrice}</Text>
                </XStack>
              ))}
              <Separator />
              <XStack justifyContent="space-between">
                <Text fontWeight="600">Total</Text>
                <Text fontWeight="600" fontSize="$5">₪{order.total}</Text>
              </XStack>
            </YStack>
          </Card>
        </YStack>
      </ScrollView>
    </>
  );
}
