import { useState } from 'react';
import { ScrollView, Alert } from 'react-native';
import {
  YStack,
  XStack,
  Text,
  Input,
  TextArea,
  Button,
  Separator,
  Label,
  Card,
  H4
} from 'tamagui';
import { Stack, useRouter } from 'expo-router';
import { useCartStore } from './CartStore';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { useLastOrderStore, useSetOrderAddress } from './useLastOrderStore';
import { useMyOrdersStore } from './orders-page-components/useMyOrdersStore';

type Location = {
  lat: number;
  lng: number;
  address: string;
};

export function CheckoutPage({ category } : { category: string; }) {
  const router = useRouter();

  const { getItemsBySupplier, totalPrice, clearCart } = useCartStore();
  const itemsBySupplier = getItemsBySupplier()

  let TotalWithoutFee = 0;
  let NumberOfOrders = 0;
  Object.entries(itemsBySupplier).map(([supplierId]) => {
    TotalWithoutFee += totalPrice(supplierId);
    NumberOfOrders++;
  });
  
  const deliveryFee = 12;                   // flat for now
  const [promo, setPromo] = useState('');
  const promoValue = promo === 'WASEL10' ? 10 : 0;
  const grandTotal = TotalWithoutFee + (deliveryFee * NumberOfOrders) - promoValue;

  // address, phone, notes
  const { address } = useSetOrderAddress();
  const [phone, setPhone] = useState('');
  const [note, setNote] = useState('');

  // payment
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card'>('cash');
  const [cardNumber, setCardNumber] = useState('');
  const [cardCvv, setCardCvv] = useState('');

  const isFormValid = () =>
    address && phone.trim() && (paymentMethod === 'cash' || cardNumber.trim());

  const { setLastOrderGroup } = useLastOrderStore();

  const placeOrder = () => {
    if (!isFormValid()) {
      Alert.alert('Missing info', 'Please fill required fields.');
      return;
    }

    const myOrders = useMyOrdersStore.getState();
    const placedAt = new Date().toISOString();

    // Split and create individual orders per supplier
    const newOrders = Object.entries(itemsBySupplier).map(([supplierId, items]) => {
      const subTotal = totalPrice(supplierId);
      const total = subTotal + deliveryFee - promoValue;
      const orderId = `W-${Date.now().toString().slice(-6)}-${supplierId}`;

      const [lat, lng] = (address?.address.split(',') ?? []).map((s) => {
        const [key, value] = s.trim().split(':');
        return parseFloat(value?.trim() ?? '0');
      });
      if (address) {
        address.lat = lat;
        address.lng = lng;
      }

      const order = {
        id: orderId,
        items,
        total,
        estimatedTime: '45-60 mins',
        address,
        phone,
        paymentMethod,
        placedAt,
        orderStatus: "Pending", // mock for now
        driverName: "Ahmad Samer"
      } as const;

      myOrders.addOrder({
        id: orderId,
        createdAt: placedAt,
        items,
        supplierId,
        total,
        status: 'Pending',
        supplierRecievedMoney: (paymentMethod === 'card'),
        address,
        phone,
        note,
        paymentMethod,
        promo,
        subTotal,
        deliveryFee,
        estimatedTime: '45-60 mins',
        driverName: "Ahmad Samer",
        driverPhone: "0595956014",
        driverLocation: { // mock for now
          lng: 35.1137,
          lat: 32.6683,
        }
      });

      return order;
    });

    setLastOrderGroup(newOrders);

    clearCart()
    router.replace('/(customer-pages)/home/<USER>')
  }

  return (
    <>
      <Stack.Screen options={{ title: 'Checkout', headerShown: true }} />

      <ScrollView contentContainerStyle={{ paddingBottom: 120 }}>
        <YStack gap="$4" p="$4">

          {/* 1. Order summary */}
          <Card p="$3" br="$6" elevation="$1">
            <H4 mb="$2">Order Summary</H4>
            {Object.entries(itemsBySupplier).map(([supplierId, items]) => (
              <YStack key={supplierId}>
                <Text fontWeight="bold">Supplier: {items[0]?.supplierName}</Text>
                {items.map(i => (
                  <XStack key={i.id} jc="space-between" ai="center" my="$1">
                    <Text>{i.qty} × {i.product.name}</Text>
                    <Text>₪{(i.finalPrice * i.qty).toFixed(2)}</Text>
                  </XStack>
                ))}
                <XStack jc="space-between">
                  <Text>Subtotal:</Text>
                  <Text>₪{totalPrice(supplierId).toFixed(2)}</Text>
                </XStack>
                <Separator my="$2" />
              </YStack>
            ))}
            <Button
              size="$2"
              icon={<Ionicons name="pencil" size={14} />}
              mt="$2"
              onPress={() => router.back()}   // or open cart sheet again
            >
              Edit Cart
            </Button>
          </Card>

          {/* 2. Address */}
          <YStack gap="$2" width={'100%'}>
            <Label>Delivery Address <Text color="red">*</Text></Label>
            <XStack gap="$2" width={'100%'} jc='space-between'>
              <Input
                width={'75%'}
                value={address?.address}
                placeholder="Set on map"
                rows={3}
              />
              <Button
                  width={'20%'}
                  size="$4"
                  onPress={() => router.push('/home/<USER>')}
              >
                Set
              </Button>
            </XStack>
          </YStack>

          {/* 3. Phone */}
          <YStack gap="$2">
            <Label>Contact Phone <Text color="red">*</Text></Label>
            <Input
              placeholder="e.g. 059-1234567"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={setPhone}
            />
          </YStack>

          {/* 4. Notes */}
          <YStack gap="$2">
            <Label>Delivery Note (optional)</Label>
            <TextArea
              placeholder="Ring the bell twice..."
              value={note}
              onChangeText={setNote}
              rows={2}
            />
          </YStack>

          {/* 5. Payment */}
          <YStack gap="$2">
            <Label>Payment Method</Label>
            <XStack gap="$2">
              <Button
                theme={paymentMethod === 'cash' ? 'active' : undefined}
                onPress={() => setPaymentMethod('cash')}
                width="45%"
              >
                Cash
              </Button>
              <Button
                theme={paymentMethod === 'card' ? 'active' : undefined}
                onPress={() => setPaymentMethod('card')}
                width="45%"
              >
                Card
              </Button>
            </XStack>

            {paymentMethod === 'card' && (
              <YStack gap="$2" mt="$2">
                <Input
                  placeholder="Card Number"
                  keyboardType="number-pad"
                  value={cardNumber}
                  onChangeText={setCardNumber}
                />
                <Input
                  placeholder="CVV"
                  keyboardType="number-pad"
                  maxLength={4}
                  value={cardCvv}
                  onChangeText={setCardCvv}
                />
              </YStack>
            )}
          </YStack>

          {/* 6. Promo */}
          <YStack gap="$2">
            <Label>Promo Code</Label>
            <Input
              placeholder="WASEL10"
              value={promo}
              onChangeText={setPromo}
            />
            {promoValue > 0 && (
              <Text color="$green10">Promo applied: -₪{promoValue}</Text>
            )}
          </YStack>

          {/* 7. Cost breakdown */}
          <Separator my="$2" />
          <XStack jc="space-between">
            <Text>Sub-Total</Text>
            <Text>₪{TotalWithoutFee.toFixed(2)}</Text>
          </XStack>
          <XStack jc="space-between">
            <Text>Delivery Fee</Text>
            <Text>₪{deliveryFee.toFixed(2)} * {NumberOfOrders}</Text>
          </XStack>
          {promoValue > 0 && (
            <XStack jc="space-between">
              <Text>Promo Discount</Text>
              <Text color="$red10">-₪{promoValue.toFixed(2)}</Text>
            </XStack>
          )}
          <Separator my="$2" />
          <XStack jc="space-between" ai="center">
            <Text fontWeight="bold" fontSize="$6">Total</Text>
            <Text fontWeight="bold" fontSize="$6">₪{grandTotal.toFixed(2)}</Text>
          </XStack>

          {/* 8. Place order */}
          <Button
            disabled={!isFormValid()}
            opacity={isFormValid() ? 1 : 0.5}
            bg="$primary"
            br="$6"
            size="$6"
            onPress={placeOrder}
            hoverStyle={{ bg: "$third" }} 
            pressStyle={{ bg: "$third" }}
          >
            <MotiView
              from={{ opacity: 0.7, scale: 0.97 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ type: 'timing', duration: 200 }}
            >
              <Text color="white" fontWeight="700">
                Confirm & Pay ₪{grandTotal.toFixed(2)}
              </Text>
            </MotiView>
          </Button>
        </YStack>
      </ScrollView>
    </>
  )
}
