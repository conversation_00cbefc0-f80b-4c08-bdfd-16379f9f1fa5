import { create } from 'zustand';

export interface Notification {
  id: string;
  type: 'order' | 'payment' | 'system' | 'review' | 'promotion';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  actionUrl?: string;
  orderId?: string;
}

interface NotificationsStore {
  notifications: Notification[];
  unreadCount: number;
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: string) => void;
  clearAll: () => void;
  getNotificationsByType: (type: Notification['type']) => Notification[];
}

// Generate sample notifications
const generateSampleNotifications = (): Notification[] => [
  {
    id: '1',
    type: 'order',
    title: 'New Order Received',
    message: 'You have received a new order #12345 worth ₪45.50',
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    read: false,
    priority: 'high',
    orderId: '12345'
  },
  {
    id: '2',
    type: 'payment',
    title: 'Payment Received',
    message: 'Payment of ₪125.00 has been processed for order #12344',
    timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    read: false,
    priority: 'medium',
    orderId: '12344'
  },
  {
    id: '3',
    type: 'review',
    title: 'New Customer Review',
    message: 'You received a 5-star review: "Amazing food and fast delivery!"',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    read: true,
    priority: 'low'
  },
  {
    id: '4',
    type: 'system',
    title: 'System Maintenance',
    message: 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
    read: true,
    priority: 'medium'
  },
  {
    id: '5',
    type: 'promotion',
    title: 'Promotion Opportunity',
    message: 'Boost your sales with our weekend promotion campaign!',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    read: false,
    priority: 'low'
  },
  {
    id: '6',
    type: 'order',
    title: 'Order Cancelled',
    message: 'Order #12343 has been cancelled by the customer',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    read: true,
    priority: 'medium',
    orderId: '12343'
  }
];

export const useSupplierNotifications = create<NotificationsStore>((set, get) => {
  const initialNotifications = generateSampleNotifications();
  const unreadCount = initialNotifications.filter(n => !n.read).length;

  return {
    notifications: initialNotifications,
    unreadCount,

    addNotification: (notification) => {
      const newNotification: Notification = {
        ...notification,
        id: Date.now().toString(),
        timestamp: new Date(),
      };

      set((state) => ({
        notifications: [newNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
      }));
    },

    markAsRead: (id) => {
      set((state) => {
        const notifications = state.notifications.map(n =>
          n.id === id ? { ...n, read: true } : n
        );
        const unreadCount = notifications.filter(n => !n.read).length;
        return { notifications, unreadCount };
      });
    },

    markAllAsRead: () => {
      set((state) => ({
        notifications: state.notifications.map(n => ({ ...n, read: true })),
        unreadCount: 0,
      }));
    },

    deleteNotification: (id) => {
      set((state) => {
        const notifications = state.notifications.filter(n => n.id !== id);
        const unreadCount = notifications.filter(n => !n.read).length;
        return { notifications, unreadCount };
      });
    },

    clearAll: () => {
      set({ notifications: [], unreadCount: 0 });
    },

    getNotificationsByType: (type) => {
      return get().notifications.filter(n => n.type === type);
    },
  };
});
