import { create } from 'zustand';
import { apiService, type User } from '../services/api';

type UserStore = {
    user: User | null;
    isLoading: boolean;
    error: string | null;
    setCurrentUser: (user: User | null) => void;
    setLoading: (loading: boolean) => void;
    setError: (error: string | null) => void;
    clearUser: () => void;
    loadUserProfile: () => Promise<void>;
}

// Mock supplier user for development
const mockSupplierUser: User = {
    id: '2',
    email: '<EMAIL>',
    role: 'supplier',
    firstName: 'Ahmad',
    lastName: 'Kefak',
    username: '3akefak',
    phoneNumber: '+970593456789',
    address: 'Nablus City Center',
    city: 'Nablus',
    country: 'Palestine',
    supplierId: '3a-kefak',
    storeName: '3a kefak',
    businessType: 'restaurant',
    openHours: '10:00 AM - 11:00 PM',
    notifications: true,
    isEmailVerified: true,
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
};

export const useCurrentUserData = create<UserStore>((set, get) => ({
    user: mockSupplierUser, // Start with mock supplier user for development
    isLoading: false,
    error: null,

    setCurrentUser: (user) => set({ user, error: null }),
    setLoading: (isLoading) => set({ isLoading }),
    setError: (error) => set({ error }),
    clearUser: () => set({ user: null, error: null }),

    loadUserProfile: async () => {
        if (!apiService.isAuthenticated()) {
            set({ user: null, error: null });
            return;
        }

        set({ isLoading: true, error: null });

        try {
            const response = await apiService.getProfile();

            if (response.success && response.data) {
                set({ user: response.data.user, isLoading: false, error: null });
            } else {
                set({ user: null, isLoading: false, error: response.message });
            }
        } catch (error) {
            set({
                user: null,
                isLoading: false,
                error: error instanceof Error ? error.message : 'Failed to load profile'
            });
        }
    },
}));