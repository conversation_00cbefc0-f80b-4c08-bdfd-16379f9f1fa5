import { Request, Response } from 'express';
import { Service } from '../models/Service';
import { validationResult } from 'express-validator';

export class ServiceController {
  // Get all active services
  static async getServices(req: Request, res: Response) {
    try {
      const services = await Service.find({ isActive: true })
        .sort({ order: 1, createdAt: 1 })
        .select('-__v');

      res.json({
        success: true,
        data: services
      });
    } catch (error) {
      console.error('Error fetching services:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch services',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Get service by key
  static async getServiceByKey(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;
      
      const service = await Service.findOne({ key, isActive: true })
        .select('-__v');

      if (!service) {
        res.status(404).json({
          success: false,
          message: 'Service not found'
        });
        return;
      }

      res.json({
        success: true,
        data: service
      });
    } catch (error) {
      console.error('Error fetching service:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch service',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Create new service (admin only)
  static async createService(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { key, label, icon, color, route, description, order } = req.body;

      // Check if service with this key already exists
      const existingService = await Service.findOne({ key });
      if (existingService) {
        res.status(409).json({
          success: false,
          message: 'Service with this key already exists'
        });
        return;
      }

      const service = new Service({
        key,
        label,
        icon,
        color,
        route,
        description,
        order: order || 0
      });

      await service.save();

      res.status(201).json({
        success: true,
        message: 'Service created successfully',
        data: service
      });
    } catch (error) {
      console.error('Error creating service:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to create service',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Update service (admin only)
  static async updateService(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { key } = req.params;
      const updateData = req.body;

      const service = await Service.findOneAndUpdate(
        { key },
        updateData,
        { new: true, runValidators: true }
      ).select('-__v');

      if (!service) {
        res.status(404).json({
          success: false,
          message: 'Service not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Service updated successfully',
        data: service
      });
    } catch (error) {
      console.error('Error updating service:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to update service',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Delete service (admin only)
  static async deleteService(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;

      const service = await Service.findOneAndDelete({ key });

      if (!service) {
        res.status(404).json({
          success: false,
          message: 'Service not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Service deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting service:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to delete service',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }

  // Toggle service active status (admin only)
  static async toggleServiceStatus(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;

      const service = await Service.findOne({ key });

      if (!service) {
        res.status(404).json({
          success: false,
          message: 'Service not found'
        });
        return;
      }

      service.isActive = !service.isActive;
      await service.save();

      res.json({
        success: true,
        message: `Service ${service.isActive ? 'activated' : 'deactivated'} successfully`,
        data: service
      });
    } catch (error) {
      console.error('Error toggling service status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to toggle service status',
        error: process.env.NODE_ENV === 'development' ? error : undefined
      });
    }
  }
}
