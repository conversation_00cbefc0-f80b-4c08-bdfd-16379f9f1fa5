 import AsyncStorage from '@react-native-async-storage/async-storage';

import { getApiBaseUrl } from './networkUtils';

// AI Service Configuration
const AI_CONFIG = {
  BACKEND_AI_URL: `${getApiBaseUrl()}/ai-chat`, // Use backend AI endpoint
  MODEL: 'gpt-4o-mini', // Using the latest efficient model
  MAX_TOKENS: 1000,
  TEMPERATURE: 0.7,
  CONVERSATION_STORAGE_KEY: 'wasel_ai_conversations',
  MAX_CONVERSATION_HISTORY: 20,
};

export interface AIMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface Conversation {
  id: string;
  messages: AIMessage[];
  createdAt: Date;
  updatedAt: Date;
  title?: string;
}

export interface AIResponse {
  success: boolean;
  message?: string;
  error?: string;
  conversationId?: string;
}

class AIService {
  private apiKey: string | null = null;
  private conversations: Map<string, Conversation> = new Map();

  constructor() {
    this.loadConversations();
    // API key is now handled by the backend
    this.apiKey = 'backend-handled';
  }

  // Initialize with API key (for development/testing) - now handled by backend
  setApiKey(key: string) {
    // API key is handled by backend, this method is kept for compatibility
    this.apiKey = 'backend-handled';
  }

  // Load conversations from storage
  private async loadConversations() {
    try {
      const stored = await AsyncStorage.getItem(AI_CONFIG.CONVERSATION_STORAGE_KEY);
      if (stored) {
        const conversationsArray: Conversation[] = JSON.parse(stored);
        conversationsArray.forEach(conv => {
          this.conversations.set(conv.id, {
            ...conv,
            createdAt: new Date(conv.createdAt),
            updatedAt: new Date(conv.updatedAt),
            messages: conv.messages.map(msg => ({
              ...msg,
              timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date()
            }))
          });
        });
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    }
  }

  // Save conversations to storage
  private async saveConversations() {
    try {
      const conversationsArray = Array.from(this.conversations.values());
      await AsyncStorage.setItem(
        AI_CONFIG.CONVERSATION_STORAGE_KEY,
        JSON.stringify(conversationsArray)
      );
    } catch (error) {
      console.error('Error saving conversations:', error);
    }
  }

  // Create a new conversation
  createConversation(): string {
    const conversationId = Date.now().toString();
    const conversation: Conversation = {
      id: conversationId,
      messages: [this.getSystemPrompt()],
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    
    this.conversations.set(conversationId, conversation);
    this.saveConversations();
    return conversationId;
  }

  // Get system prompt with Wasel context
  private getSystemPrompt(): AIMessage {
    return {
      role: 'system',
      content: `You are Wasel AI Assistant, a helpful and professional customer service AI for Wasel - a comprehensive delivery and logistics platform in Palestine.

ABOUT WASEL:
- Wasel is a delivery platform connecting customers with local suppliers
- Services include: food delivery, grocery delivery, pharmacy delivery, package sending, pickup requests
- Operating in Palestine with focus on Nablus and surrounding areas
- Supports both customers and suppliers with a mobile app

YOUR CAPABILITIES:
1. Order Management: Help track orders, modify orders, cancel orders, view order history
2. Supplier Discovery: Help find restaurants, groceries, pharmacies, and other suppliers
3. Package Services: Assist with sending packages and requesting pickups
4. Account Support: Help with profile management, payment methods, addresses
5. General Support: Answer questions about services, delivery times, fees, policies

PERSONALITY:
- Professional yet friendly and approachable
- Helpful and solution-oriented
- Culturally aware and respectful
- Use appropriate Arabic greetings when suitable (مرحبا، أهلاً وسهلاً)
- Empathetic to customer concerns

RESPONSE GUIDELINES:
- Keep responses concise but comprehensive
- Offer specific next steps when possible
- Ask clarifying questions when needed
- Provide relevant quick actions or suggestions
- Use emojis appropriately to enhance communication
- If you cannot help with something, clearly explain limitations and suggest alternatives

IMPORTANT:
- Always prioritize customer satisfaction
- Escalate complex issues to human support when necessary
- Maintain user privacy and data security
- Be honest about what you can and cannot do
- Focus on Wasel's services and capabilities

Remember: You're representing Wasel's brand, so maintain high standards of customer service.`,
      timestamp: new Date()
    };
  }

  // Send message to AI and get response via backend
  async sendMessage(
    conversationId: string,
    userMessage: string,
    userContext?: any
  ): Promise<AIResponse> {
    try {
      const conversation = this.conversations.get(conversationId);
      if (!conversation) {
        return {
          success: false,
          error: 'Conversation not found',
        };
      }

      // Add user message to conversation
      const userMsg: AIMessage = {
        role: 'user',
        content: userMessage,
        timestamp: new Date(),
      };
      conversation.messages.push(userMsg);

      // Prepare messages for API (limit history to prevent token overflow)
      const messagesToSend = conversation.messages.slice(-AI_CONFIG.MAX_CONVERSATION_HISTORY);

      // Call backend AI API
      const response = await fetch(`${AI_CONFIG.BACKEND_AI_URL}/send-message`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversationId,
          message: userMessage,
          context: userContext,
          messages: messagesToSend.map(msg => ({
            role: msg.role,
            content: msg.content,
          }))
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Network error' }));
        throw new Error(errorData.error || 'AI API request failed');
      }

      const data = await response.json();
      const aiMessage = data.message;

      if (!aiMessage) {
        throw new Error('No response from AI');
      }

      // Add AI response to conversation
      const assistantMsg: AIMessage = {
        role: 'assistant',
        content: aiMessage,
        timestamp: new Date(),
      };
      conversation.messages.push(assistantMsg);
      conversation.updatedAt = new Date();

      // Update conversation title if it's the first exchange
      if (!conversation.title && conversation.messages.length >= 3) {
        conversation.title = this.generateConversationTitle(userMessage);
      }

      // Save updated conversation
      this.conversations.set(conversationId, conversation);
      await this.saveConversations();

      return {
        success: true,
        message: aiMessage,
        conversationId,
      };

    } catch (error) {
      console.error('AI Service Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Generate a title for the conversation based on the first user message
  private generateConversationTitle(firstMessage: string): string {
    const message = firstMessage.toLowerCase();
    
    if (message.includes('track') || message.includes('order')) {
      return 'Order Tracking';
    } else if (message.includes('supplier') || message.includes('restaurant')) {
      return 'Supplier Search';
    } else if (message.includes('package') || message.includes('send')) {
      return 'Package Delivery';
    } else if (message.includes('pickup')) {
      return 'Pickup Request';
    } else if (message.includes('help') || message.includes('support')) {
      return 'Customer Support';
    } else {
      return 'General Inquiry';
    }
  }

  // Get conversation by ID
  getConversation(conversationId: string): Conversation | null {
    return this.conversations.get(conversationId) || null;
  }

  // Get all conversations
  getAllConversations(): Conversation[] {
    return Array.from(this.conversations.values())
      .sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
  }

  // Delete conversation
  async deleteConversation(conversationId: string): Promise<boolean> {
    try {
      const deleted = this.conversations.delete(conversationId);
      if (deleted) {
        await this.saveConversations();
      }
      return deleted;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      return false;
    }
  }

  // Clear all conversations
  async clearAllConversations(): Promise<boolean> {
    try {
      this.conversations.clear();
      await AsyncStorage.removeItem(AI_CONFIG.CONVERSATION_STORAGE_KEY);
      return true;
    } catch (error) {
      console.error('Error clearing conversations:', error);
      return false;
    }
  }

  // Get fallback response when AI is not available
  getFallbackResponse(userMessage: string): string {
    const message = userMessage.toLowerCase();
    
    if (message.includes('track') || message.includes('order')) {
      return "I'd be happy to help you track your order! 📦\n\nTo track an order, please provide your order number. You can find it in your email confirmation or in the Orders tab.\n\nAlternatively, you can check your recent orders in the Orders section of the app.";
    }
    
    if (message.includes('supplier') || message.includes('restaurant')) {
      return "I can help you find suppliers! 🏪\n\nYou can browse suppliers by:\n• Category (restaurants, groceries, pharmacy)\n• Location (use the map feature)\n• Ratings and reviews\n\nTry using the search function on the home page or browse by categories.";
    }
    
    if (message.includes('package') || message.includes('send')) {
      return "I can help you send a package! 📮\n\nTo send a package:\n1. Go to 'Send Package' from the home screen\n2. Enter pickup and delivery addresses\n3. Specify package details\n4. Choose delivery time\n\nWould you like me to guide you through the process?";
    }
    
    return "Thank you for contacting Wasel support! 👋\n\nI'm here to help with:\n• Order tracking and management\n• Finding suppliers and restaurants\n• Package delivery services\n• Account support\n\nHow can I assist you today?";
  }
}

export const aiService = new AIService();
 
