import { useForm } from 'react-hook-form'
import { LoginGUI } from './LoginGUI';
import { SignupGUI } from './SignupGUI';

type ScreenContentProps = {
  title: string;
  path: string;
  children?: React.ReactNode;
};

export const ScreenContent = ({ title, path, children }: ScreenContentProps) => {
  const methods = useForm();

  const onSubmit = methods.handleSubmit((data) => {
    console.log('Form data:', data);
  })

  if (title === "Login") {
    return <LoginGUI methods={methods} children={children} />;
  } else if (title === "Signup") {
    return <SignupGUI methods={methods} onSubmit={onSubmit} children={children} />;
  } else {
    return null; // Return null for unknown titles
  }
};

