import React from 'react';
import { Outlet } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Truck, Package, MapPin, Star } from 'lucide-react';
import Logo from '../common/Logo';

const AuthLayout: React.FC = () => {
  const floatingElements = [
    { icon: Truck, delay: 0, x: 100, y: 50 },
    { icon: Package, delay: 0.5, x: -80, y: 100 },
    { icon: MapPin, delay: 1, x: 120, y: -60 },
    { icon: Star, delay: 1.5, x: -100, y: -80 },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        {floatingElements.map((element, index) => {
          const Icon = element.icon;
          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, scale: 0 }}
              animate={{
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
                x: [element.x, element.x + 20, element.x],
                y: [element.y, element.y - 20, element.y],
              }}
              transition={{
                duration: 4,
                delay: element.delay,
                repeat: Infinity,
                repeatType: "reverse",
              }}
              className="absolute"
              style={{
                left: `${50 + element.x}px`,
                top: `${50 + element.y}px`
              }}
            >
              <Icon size={32} className="text-primary-300" />
            </motion.div>
          );
        })}
      </div>

      {/* Gradient Orbs */}
      <div className="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-primary-400/20 to-secondary-400/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-secondary-400/20 to-primary-400/20 rounded-full blur-3xl"></div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="w-full max-w-md"
        >
          <div className="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 p-8 relative">
            {/* Logo Section */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-center mb-8"
            >
              <div className="flex justify-center mb-4">
                <Logo size="lg" />
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Outlet />
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default AuthLayout;
