import { Platform, ScrollView, TouchableWithoutFeedback } from 'react-native';
import { View, Text, Button, XStack, YStack } from 'tamagui';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useMyOrdersStore } from './useMyOrdersStore';
import { useEffect, useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { MotiView } from 'moti';
import { Dimensions } from 'react-native';
import { Linking } from 'react-native';
// Use react-native-web-maps directly for web
import MapView, { <PERSON><PERSON>, <PERSON>yline } from 'react-native-web-maps';

export function OrderTracking() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const order = useMyOrdersStore((s) => s.orders.find((o) => o.id === id));
  const [currentLocation, setCurrentLocation] = useState<[number, number] | null>(null);
  const [driverLocation, setDriverLocation] = useState<[number, number] | null>(null);
  const [estimatedTime, setEstimatedTime] = useState<string>('');
  const { width, height } = Dimensions.get('window');

  useEffect(() => {
    if (!order) return;

    // Simulate real-time tracking
    const interval = setInterval(() => {
      // Mock driver location updates
      const mockDriverLat = 31.9539 + (Math.random() - 0.5) * 0.01;
      const mockDriverLng = 35.9106 + (Math.random() - 0.5) * 0.01;
      setDriverLocation([mockDriverLng, mockDriverLat]);
      
      // Mock estimated time
      const times = ['5 min', '8 min', '12 min', '15 min'];
      setEstimatedTime(times[Math.floor(Math.random() * times.length)]);
    }, 3000);

    return () => clearInterval(interval);
  }, [order]);

  if (!order) {
    return (
      <View flex={1} justifyContent="center" alignItems="center" padding="$4">
        <Text>Order not found</Text>
        <Button onPress={() => router.back()} mt="$4">
          Go Back
        </Button>
      </View>
    );
  }

  const defaultRegion = {
    latitude: 32.2211,
    longitude: 35.2544,
    latitudeDelta: 0.02,
    longitudeDelta: 0.02,
  };

  const handleCallDriver = () => {
    Linking.openURL('tel:+970599123456');
  };

  const handleMessageDriver = () => {
    Linking.openURL('sms:+970599123456');
  };

  return (
    <View flex={1}>
      {/* Web Map for Order Tracking */}
      <MapView
        style={{ width, height: height * 0.6 }}
        initialRegion={defaultRegion}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {/* Driver Location Marker */}
        {driverLocation && (
          <Marker
            coordinate={{
              latitude: driverLocation[1],
              longitude: driverLocation[0],
            }}
            title="Driver Location"
            description="Your delivery driver"
          >
            <View
              style={{
                width: 40,
                height: 40,
                borderRadius: 20,
                backgroundColor: '#007AFF',
                justifyContent: 'center',
                alignItems: 'center',
                borderWidth: 2,
                borderColor: '#fff',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.3,
                shadowRadius: 3,
                elevation: 5,
              }}
            >
              <Ionicons name="car" size={20} color="white" />
            </View>
          </Marker>
        )}

        {/* Delivery Address Marker */}
        {order.address && (
          <Marker
            coordinate={{
              latitude: order.address.lat,
              longitude: order.address.lng,
            }}
            title="Delivery Address"
            description={order.address.address}
            pinColor="red"
          />
        )}

        {/* Route Polyline */}
        {driverLocation && order.address && (
          <Polyline
            coordinates={[
              {
                latitude: driverLocation[1],
                longitude: driverLocation[0],
              },
              {
                latitude: order.address.lat,
                longitude: order.address.lng,
              },
            ]}
            strokeColor="#007AFF"
            strokeWidth={3}
            lineDashPattern={[5, 5]}
          />
        )}
      </MapView>

      {/* Order Details Panel */}
      <ScrollView style={{ flex: 1 }} contentContainerStyle={{ padding: 16 }}>
        <YStack space="$4">
          {/* Order Status */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500 }}
          >
            <View
              backgroundColor="$blue2"
              padding="$4"
              borderRadius="$4"
              borderWidth={1}
              borderColor="$blue6"
            >
              <XStack justifyContent="space-between" alignItems="center">
                <YStack>
                  <Text fontSize="$6" fontWeight="bold" color="$blue11">
                    Order #{order.id.slice(-6)}
                  </Text>
                  <Text fontSize="$4" color="$blue10">
                    Status: {order.status}
                  </Text>
                </YStack>
                <View
                  backgroundColor="$blue6"
                  padding="$2"
                  borderRadius="$2"
                >
                  <Ionicons name="time" size={20} color="white" />
                </View>
              </XStack>
              
              {estimatedTime && (
                <Text fontSize="$5" fontWeight="600" color="$blue11" mt="$2">
                  Estimated arrival: {estimatedTime}
                </Text>
              )}
            </View>
          </MotiView>

          {/* Driver Contact */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500, delay: 200 }}
          >
            <View
              backgroundColor="$gray2"
              padding="$4"
              borderRadius="$4"
              borderWidth={1}
              borderColor="$gray6"
            >
              <Text fontSize="$5" fontWeight="bold" mb="$3">
                Driver Contact
              </Text>
              <XStack space="$3">
                <TouchableWithoutFeedback onPress={handleCallDriver}>
                  <View
                    backgroundColor="$green6"
                    padding="$3"
                    borderRadius="$3"
                    flex={1}
                    alignItems="center"
                  >
                    <Ionicons name="call" size={20} color="white" />
                    <Text color="white" fontSize="$3" mt="$1">
                      Call
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
                
                <TouchableWithoutFeedback onPress={handleMessageDriver}>
                  <View
                    backgroundColor="$blue6"
                    padding="$3"
                    borderRadius="$3"
                    flex={1}
                    alignItems="center"
                  >
                    <Ionicons name="chatbubble" size={20} color="white" />
                    <Text color="white" fontSize="$3" mt="$1">
                      Message
                    </Text>
                  </View>
                </TouchableWithoutFeedback>
              </XStack>
            </View>
          </MotiView>

          {/* Order Items */}
          <MotiView
            from={{ opacity: 0, translateY: 20 }}
            animate={{ opacity: 1, translateY: 0 }}
            transition={{ type: 'timing', duration: 500, delay: 400 }}
          >
            <View
              backgroundColor="$gray2"
              padding="$4"
              borderRadius="$4"
              borderWidth={1}
              borderColor="$gray6"
            >
              <Text fontSize="$5" fontWeight="bold" mb="$3">
                Order Items
              </Text>
              <YStack space="$2">
                {order.items.map((item, index) => (
                  <XStack key={index} justifyContent="space-between" alignItems="center">
                    <YStack flex={1}>
                      <Text fontSize="$4" fontWeight="600">
                        {item.product.name}
                      </Text>
                      <Text fontSize="$3" color="$gray10">
                        Qty: {item.qty}
                      </Text>
                    </YStack>
                    <Text fontSize="$4" fontWeight="bold">
                      ${item.finalPrice.toFixed(2)}
                    </Text>
                  </XStack>
                ))}
              </YStack>
              
              <View height={1} backgroundColor="$gray6" my="$3" />
              
              <XStack justifyContent="space-between" alignItems="center">
                <Text fontSize="$5" fontWeight="bold">
                  Total
                </Text>
                <Text fontSize="$5" fontWeight="bold" color="$blue11">
                  ${order.total.toFixed(2)}
                </Text>
              </XStack>
            </View>
          </MotiView>

          {/* Delivery Address */}
          {order.address && (
            <MotiView
              from={{ opacity: 0, translateY: 20 }}
              animate={{ opacity: 1, translateY: 0 }}
              transition={{ type: 'timing', duration: 500, delay: 600 }}
            >
              <View
                backgroundColor="$gray2"
                padding="$4"
                borderRadius="$4"
                borderWidth={1}
                borderColor="$gray6"
              >
                <Text fontSize="$5" fontWeight="bold" mb="$2">
                  Delivery Address
                </Text>
                <Text fontSize="$4" color="$gray11">
                  {order.address.address}
                </Text>
              </View>
            </MotiView>
          )}
        </YStack>
      </ScrollView>
    </View>
  );
}

export default OrderTracking;
