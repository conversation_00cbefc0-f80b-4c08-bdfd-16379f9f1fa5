import React, { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Package,
  Clock,
  CheckCircle,
  Truck,
  Eye,
  MapPin,
  Phone,
  CreditCard,
  Calendar,
  Filter,
  Search,
  X,
  Navigation,
  ArrowLeft,
  Star,
  Heart,
  Zap,
  TrendingUp,
  Award,
  Sparkles,
  ShoppingBag,
  User,
  ChevronDown,
  ChevronUp,
  RotateCcw
} from 'lucide-react';
import { useOrdersStore } from '../../stores/ordersStore';

type OrderStatus = 'All' | 'Delivered' | 'On the Way' | 'Preparing' | 'Pending' | 'Confirmed' | 'Ready' | 'Cancelled';

const OrdersPage: React.FC = () => {
  const navigate = useNavigate();
  const { orders } = useOrdersStore();
  const [selectedFilter, setSelectedFilter] = useState<OrderStatus>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedOrder, setSelectedOrder] = useState<string | null>(null);

  // State for animations and interactions
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [expandedOrders, setExpandedOrders] = useState<Set<string>>(new Set());

  // Handle scroll for header animation with ultra-smooth debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    let rafId: number;

    const handleScroll = () => {
      rafId = requestAnimationFrame(() => {
        const currentScrollY = window.scrollY;
        setScrollY(currentScrollY);

        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          setIsHeaderCompact(currentScrollY > 120);
        }, 50);
      });
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
      cancelAnimationFrame(rafId);
    };
  }, []);

  const filters: { key: OrderStatus; label: string; color: string }[] = [
    { key: 'All', label: 'All Orders', color: 'bg-gray-100 text-gray-700' },
    { key: 'Delivered', label: 'Delivered', color: 'bg-green-100 text-green-700' },
    { key: 'On the Way', label: 'On the Way', color: 'bg-orange-100 text-orange-700' },
    { key: 'Preparing', label: 'Preparing', color: 'bg-blue-100 text-blue-700' },
    { key: 'Pending', label: 'Pending', color: 'bg-yellow-100 text-yellow-700' }
  ];

  const filteredOrders = useMemo(() => {
    let filteredOrders = selectedFilter === 'All' ? orders : orders.filter(order => order.status === selectedFilter);

    if (searchQuery.trim()) {
      filteredOrders = filteredOrders.filter(order =>
        order.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        order.items.some(item => item.product.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return filteredOrders.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }, [orders, selectedFilter, searchQuery]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Delivered': return <CheckCircle size={16} className="text-green-600" />;
      case 'On the Way': return <Truck size={16} className="text-orange-600" />;
      case 'Preparing': return <Clock size={16} className="text-blue-600" />;
      default: return <Package size={16} className="text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Delivered': return 'text-green-600 bg-green-50 border-green-200';
      case 'On the Way': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'Preparing': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return {
      date: date.toLocaleDateString(),
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    };
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
        {/* Animated gradient orbs */}
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.4, 0.7, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-purple-500/30 to-pink-600/30 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.2, 0.5, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 4
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-indigo-500/20 to-cyan-500/20 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.3, 0.6, 0.3],
          }}
          transition={{
            duration: 9,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute top-1/4 right-1/4 w-64 h-64 bg-gradient-to-br from-emerald-500/20 to-blue-500/20 rounded-full blur-3xl"
        />
      </div>

      {/* Sticky Header with Ultra-Smooth Scroll Animation */}
      <motion.div
        className="fixed left-0 right-0 transition-all duration-500"
        animate={{
          top: isHeaderCompact ? "0px" : "64px",
          zIndex: isHeaderCompact ? 50 : 40,
          backgroundColor: isHeaderCompact
            ? "rgba(15, 23, 42, 0.95)"
            : "rgba(15, 23, 42, 0)",
          backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
          borderBottom: isHeaderCompact
            ? "1px solid rgba(255, 255, 255, 0.1)"
            : "1px solid rgba(255, 255, 255, 0)",
        }}
        transition={{
          duration: 0.6,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "tween"
        }}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            animate={{
              paddingTop: isHeaderCompact ? "1rem" : "1.5rem",
              paddingBottom: isHeaderCompact ? "1rem" : "1.5rem",
            }}
            transition={{
              duration: 0.6,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
          >
            <div className="flex items-center justify-between">
              {/* Left side - Logo and title */}
              <div className="flex items-center gap-3">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="p-2 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl"
                >
                  <ShoppingBag className="w-6 h-6 text-white" />
                </motion.div>
                <div>
                  <motion.h1
                    className="font-bold bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent"
                    animate={{
                      fontSize: isHeaderCompact ? "1.5rem" : "1.75rem",
                    }}
                    transition={{
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      type: "tween"
                    }}
                  >
                    My Orders
                  </motion.h1>
                  <motion.p
                    className="text-white/60"
                    animate={{
                      fontSize: isHeaderCompact ? "0.875rem" : "1rem",
                      opacity: isHeaderCompact ? 0.8 : 1,
                    }}
                    transition={{
                      duration: 0.6,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      type: "tween"
                    }}
                  >
                    Track and manage your orders
                  </motion.p>
                </div>
              </div>

              {/* Right side - Compact search bar (only when scrolled) */}
              <AnimatePresence>
                {isHeaderCompact && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, x: 20 }}
                    animate={{ opacity: 1, scale: 1, x: 0 }}
                    exit={{ opacity: 0, scale: 0.8, x: 20 }}
                    transition={{
                      duration: 0.4,
                      ease: [0.25, 0.46, 0.45, 0.94],
                      type: "tween"
                    }}
                    className="flex items-center gap-3 bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 px-4 py-2 min-w-[300px]"
                  >
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                      className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg"
                    >
                      <Search className="w-4 h-4 text-white" />
                    </motion.div>
                    <input
                      type="text"
                      placeholder="Search orders..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-sm font-medium"
                    />
                    {searchQuery.trim() && (
                      <motion.button
                        onClick={() => setSearchQuery('')}
                        whileHover={{
                          scale: 1.1,
                          transition: { duration: 0.2, ease: "easeOut" }
                        }}
                        whileTap={{
                          scale: 0.9,
                          transition: { duration: 0.1, ease: "easeInOut" }
                        }}
                        className="p-1 hover:bg-white/10 rounded-lg transition-colors"
                      >
                        <X className="w-4 h-4 text-white/70" />
                      </motion.button>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Content - Dynamic padding based on header state */}
      <motion.div
        className="relative z-10 pb-20"
        animate={{
          paddingTop: isHeaderCompact ? "80px" : "160px",
        }}
        transition={{
          duration: 0.8,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "tween"
        }}
      >
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 space-y-8">

          {/* Premium Search Bar - Hidden when header is compact */}
          <AnimatePresence>
            {!isHeaderCompact && (
              <motion.div
                initial={{ opacity: 0, y: 50, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -20, scale: 0.95 }}
                transition={{
                  duration: 0.8,
                  delay: 0.1,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "tween"
                }}
                className="mb-8"
              >
            <motion.div
              whileHover={{
                scale: 1.02,
                y: -2,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-6 shadow-2xl"
            >
              <div className="flex items-center gap-4">
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl"
                >
                  <Search className="w-6 h-6 text-white" />
                </motion.div>
                <input
                  type="text"
                  placeholder="Search orders, restaurants, or items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="flex-1 bg-transparent text-white placeholder-white/60 outline-none text-lg font-medium"
                />
                {searchQuery.trim() && (
                  <motion.button
                    onClick={() => setSearchQuery('')}
                    whileHover={{
                      scale: 1.1,
                      transition: { duration: 0.2, ease: "easeOut" }
                    }}
                    whileTap={{
                      scale: 0.9,
                      transition: { duration: 0.1, ease: "easeInOut" }
                    }}
                    className="p-2 hover:bg-white/10 rounded-xl transition-colors backdrop-blur-sm border border-white/10"
                  >
                    <X className="w-5 h-5 text-white/70" />
                  </motion.button>
                )}
              </div>
            </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Premium Filter Bar */}
          <motion.div
            initial={{ opacity: 0, y: 50, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{
              duration: 0.8,
              delay: 0.2,
              ease: [0.25, 0.46, 0.45, 0.94],
              type: "tween"
            }}
            className="mb-8"
          >
            <motion.div
              whileHover={{
                scale: 1.02,
                y: -2,
                transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 shadow-2xl"
            >
              <div className="flex items-center gap-4 mb-6">
                <motion.div
                  animate={{ scale: [1, 1.1, 1] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl"
                >
                  <Filter className="w-6 h-6 text-white" />
                </motion.div>
                <h3 className="text-xl font-bold text-white">Filter Orders</h3>
                <motion.div
                  animate={{ rotate: [0, 360] }}
                  transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
                  className="ml-auto p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl"
                >
                  <Sparkles className="w-5 h-5 text-white" />
                </motion.div>
              </div>
              <div className="flex flex-wrap gap-3">
                {filters.map((filter, index) => (
                  <motion.button
                    key={filter.key}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: index * 0.1,
                      duration: 0.5,
                      ease: [0.25, 0.46, 0.45, 0.94]
                    }}
                    onClick={() => setSelectedFilter(filter.key)}
                    whileHover={{
                      scale: 1.05,
                      y: -2,
                      transition: { duration: 0.2, ease: "easeOut" }
                    }}
                    whileTap={{
                      scale: 0.95,
                      transition: { duration: 0.1, ease: "easeInOut" }
                    }}
                    className={`px-6 py-3 rounded-2xl text-sm font-bold shadow-lg transition-all duration-300 ${
                      selectedFilter === filter.key
                        ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-purple-500/25'
                        : 'bg-white/10 text-white border border-white/20 hover:bg-white/20'
                    }`}
                  >
                    {filter.label}
                  </motion.button>
                ))}
              </div>
            </motion.div>
          </motion.div>

        {/* Orders List */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="space-y-6"
        >
          {filteredOrders.length === 0 ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              transition={{
                duration: 0.8,
                ease: [0.25, 0.46, 0.45, 0.94],
                type: "tween"
              }}
              className="bg-white/10 backdrop-blur-xl rounded-3xl p-16 text-center shadow-2xl border border-white/20"
            >
              <motion.div
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 4,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="mb-8"
              >
                <div className="relative">
                  <Package size={80} className="text-white/60 mx-auto" />
                  <motion.div
                    animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full"
                  />
                </div>
              </motion.div>
              <h3 className="text-3xl font-bold text-white mb-4 bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                No Orders Found
              </h3>
              <p className="text-white/70 text-lg mb-8">
                {searchQuery.trim()
                  ? `No orders match "${searchQuery}"`
                  : selectedFilter === 'All'
                    ? "You haven't placed any orders yet"
                    : `No ${selectedFilter.toLowerCase()} orders found`
                }
              </p>
              <motion.button
                onClick={() => navigate('/customer/home')}
                whileHover={{
                  scale: 1.05,
                  y: -2,
                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                }}
                whileTap={{
                  scale: 0.95,
                  transition: { duration: 0.15, ease: "easeInOut" }
                }}
                className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 flex items-center gap-3 mx-auto"
                style={{
                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                }}
              >
                <ShoppingBag className="w-5 h-5" />
                Start Shopping
              </motion.button>
            </motion.div>
          ) : (
            <AnimatePresence mode="wait" key={selectedFilter}>
              <motion.div
                key={selectedFilter}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{
                  duration: 0.5,
                  ease: [0.25, 0.46, 0.45, 0.94],
                  type: "tween"
                }}
                className="space-y-6"
              >
                {filteredOrders.map((order, index) => {
                  const isExpanded = expandedOrders.has(order.id);
                  return (
                    <motion.div
                      key={order.id}
                      initial={{ opacity: 0, y: 30, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      transition={{
                        duration: 0.6,
                        delay: index * 0.1,
                        ease: [0.25, 0.46, 0.45, 0.94],
                        type: "tween"
                      }}
                      whileHover={{
                        y: -8,
                        scale: 1.02,
                        transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                      }}
                      className="bg-white/10 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 overflow-hidden"
                    >
                    {/* Premium Order Header */}
                    <div className="p-8 border-b border-white/10">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-6">
                          <motion.div
                            whileHover={{
                              rotate: 360,
                              transition: { duration: 0.5, ease: "easeInOut" }
                            }}
                            className="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-500 to-blue-500 flex items-center justify-center shadow-2xl"
                          >
                            <Package className="text-white" size={28} />
                          </motion.div>
                          <div>
                            <h3 className="font-bold text-2xl text-white mb-1">#{order.id.slice(-8)}</h3>
                            <div className="flex items-center gap-2">
                              <motion.div
                                animate={{ scale: [1, 1.1, 1] }}
                                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                className="w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full"
                              />
                              <p className="text-white/80 text-lg font-medium">{order.supplier.name}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <motion.div
                            whileHover={{
                              scale: 1.05,
                              transition: { duration: 0.2, ease: "easeOut" }
                            }}
                            className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl text-sm font-bold border bg-white/10 border-white/20 text-white shadow-lg backdrop-blur-sm mb-2"
                          >
                            <motion.div
                              animate={{ rotate: 360 }}
                              transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                            >
                              {getStatusIcon(order.status)}
                            </motion.div>
                            {order.status}
                          </motion.div>
                          <p className="text-white/60 text-sm">
                            {formatDate(order.createdAt).date} • {formatDate(order.createdAt).time}
                          </p>
                        </div>
                      </div>

                      {/* Expand/Collapse Button */}
                      <motion.button
                        onClick={() => {
                          const newExpanded = new Set(expandedOrders);
                          if (isExpanded) {
                            newExpanded.delete(order.id);
                          } else {
                            newExpanded.add(order.id);
                          }
                          setExpandedOrders(newExpanded);
                        }}
                        whileHover={{
                          scale: 1.05,
                          transition: { duration: 0.2, ease: "easeOut" }
                        }}
                        whileTap={{
                          scale: 0.95,
                          transition: { duration: 0.1, ease: "easeInOut" }
                        }}
                        className="w-full flex items-center justify-center gap-3 py-3 bg-white/5 rounded-2xl border border-white/10 text-white font-medium hover:bg-white/10 transition-colors"
                      >
                        <span>{isExpanded ? 'Hide Details' : 'Show Details'}</span>
                        <motion.div
                          animate={{ rotate: isExpanded ? 180 : 0 }}
                          transition={{ duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }}
                        >
                          <ChevronDown className="w-5 h-5" />
                        </motion.div>
                      </motion.button>
                    </div>

                    {/* Collapsible Order Details */}
                    <AnimatePresence>
                      {isExpanded && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{
                            duration: 0.5,
                            ease: [0.25, 0.46, 0.45, 0.94],
                            type: "tween"
                          }}
                          className="overflow-hidden"
                        >
                          <div className="p-8 space-y-8">
                            {/* Order Items Section */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.1, duration: 0.5 }}
                              className="bg-white/5 rounded-2xl p-6 border border-white/10"
                            >
                              <div className="flex items-center gap-3 mb-6">
                                <motion.div
                                  animate={{ rotate: [0, 360] }}
                                  transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                                  className="p-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl"
                                >
                                  <Package className="w-5 h-5 text-white" />
                                </motion.div>
                                <h4 className="font-bold text-white text-lg">Order Items</h4>
                              </div>
                              <div className="space-y-3">
                                {order.items.map((item, idx) => (
                                  <motion.div
                                    key={idx}
                                    initial={{ opacity: 0, x: -20 }}
                                    animate={{ opacity: 1, x: 0 }}
                                    transition={{ delay: idx * 0.1, duration: 0.4 }}
                                    whileHover={{
                                      scale: 1.02,
                                      x: 4,
                                      transition: { duration: 0.2, ease: "easeOut" }
                                    }}
                                    className="flex justify-between items-center py-4 px-4 bg-white/5 rounded-xl border border-white/10"
                                  >
                                    <div className="flex items-center gap-3">
                                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-blue-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                                        {item.qty}
                                      </div>
                                      <span className="text-white font-medium">
                                        {item.product.name}
                                      </span>
                                    </div>
                                    <span className="font-bold text-white text-lg">
                                      ₪{(item.finalPrice * item.qty).toFixed(2)}
                                    </span>
                                  </motion.div>
                                ))}
                              </div>
                            </motion.div>

                            {/* Delivery Info Section */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.2, duration: 0.5 }}
                              className="bg-white/5 rounded-2xl p-6 border border-white/10"
                            >
                              <div className="flex items-center gap-3 mb-6">
                                <motion.div
                                  animate={{ y: [0, -5, 0] }}
                                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                  className="p-2 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl"
                                >
                                  <MapPin className="w-5 h-5 text-white" />
                                </motion.div>
                                <h4 className="font-bold text-white text-lg">Delivery Details</h4>
                              </div>
                              <div className="space-y-4">
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg">
                                    <MapPin className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">{order.address}</span>
                                </motion.div>
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                                    <Phone className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">{order.phone}</span>
                                </motion.div>
                                <motion.div
                                  whileHover={{
                                    scale: 1.02,
                                    x: 4,
                                    transition: { duration: 0.2, ease: "easeOut" }
                                  }}
                                  className="flex items-center gap-4 p-4 bg-white/5 rounded-xl border border-white/10"
                                >
                                  <div className="p-2 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-lg">
                                    <Clock className="w-4 h-4 text-white" />
                                  </div>
                                  <span className="text-white font-medium">Est. {order.estimatedTime}</span>
                                </motion.div>
                              </div>
                            </motion.div>

                            {/* Payment & Driver Info Section */}
                            <div className="grid md:grid-cols-2 gap-6">
                              {/* Payment Info */}
                              <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ delay: 0.3, duration: 0.5 }}
                                className="bg-white/5 rounded-2xl p-6 border border-white/10"
                              >
                                <div className="flex items-center gap-3 mb-6">
                                  <motion.div
                                    animate={{ scale: [1, 1.1, 1] }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                                    className="p-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl"
                                  >
                                    <CreditCard className="w-5 h-5 text-white" />
                                  </motion.div>
                                  <h4 className="font-bold text-white text-lg">Payment</h4>
                                </div>
                                <div className="space-y-4">
                                  <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                    <span className="text-white/70">Payment Method:</span>
                                    <span className="font-bold text-white capitalize">{order.paymentMethod}</span>
                                  </div>
                                  <motion.div
                                    whileHover={{
                                      scale: 1.02,
                                      transition: { duration: 0.2, ease: "easeOut" }
                                    }}
                                    className="flex justify-between items-center p-4 bg-gradient-to-r from-emerald-500/20 to-teal-500/20 rounded-xl border border-emerald-400/30"
                                  >
                                    <span className="text-white font-bold text-lg">Total:</span>
                                    <span className="text-white font-bold text-2xl">₪{order.total.toFixed(2)}</span>
                                  </motion.div>
                                </div>
                              </motion.div>

                              {/* Driver Info */}
                              {order.driverName && (
                                <motion.div
                                  initial={{ opacity: 0, y: 20 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  transition={{ delay: 0.4, duration: 0.5 }}
                                  className="bg-white/5 rounded-2xl p-6 border border-white/10"
                                >
                                  <div className="flex items-center gap-3 mb-6">
                                    <motion.div
                                      animate={{ rotate: [0, 10, -10, 0] }}
                                      transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
                                      className="p-2 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-xl"
                                    >
                                      <Truck className="w-5 h-5 text-white" />
                                    </motion.div>
                                    <h4 className="font-bold text-white text-lg">Driver</h4>
                                  </div>
                                  <div className="space-y-3">
                                    <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                      <span className="text-white/70">Name:</span>
                                      <span className="font-bold text-white">{order.driverName}</span>
                                    </div>
                                    <div className="flex justify-between items-center p-4 bg-white/5 rounded-xl border border-white/10">
                                      <span className="text-white/70">Phone:</span>
                                      <span className="font-bold text-white">{order.driverPhone}</span>
                                    </div>
                                  </div>
                                </motion.div>
                              )}
                            </div>

                            {/* Premium Action Buttons */}
                            <motion.div
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.5, duration: 0.5 }}
                              className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-white/10"
                            >
                              <motion.button
                                onClick={() => navigate(`/customer/order-details?orderId=${order.id}`)}
                                whileHover={{
                                  scale: 1.05,
                                  y: -2,
                                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                }}
                                whileTap={{
                                  scale: 0.95,
                                  transition: { duration: 0.15, ease: "easeInOut" }
                                }}
                                className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-blue-500/25 flex items-center justify-center gap-3"
                                style={{
                                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                }}
                              >
                                <Eye className="w-5 h-5" />
                                View Details
                              </motion.button>

                              {(order.status === 'Preparing' || order.status === 'On the Way') && (
                                <motion.button
                                  onClick={() => navigate(`/customer/order-tracking?orderId=${order.id}`)}
                                  whileHover={{
                                    scale: 1.05,
                                    y: -2,
                                    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                  }}
                                  whileTap={{
                                    scale: 0.95,
                                    transition: { duration: 0.15, ease: "easeInOut" }
                                  }}
                                  className="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-green-500/25 flex items-center justify-center gap-3"
                                  style={{
                                    transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                  }}
                                >
                                  <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                                  >
                                    <Navigation className="w-5 h-5" />
                                  </motion.div>
                                  Track Live
                                </motion.button>
                              )}

                              <motion.button
                                onClick={() => navigate(`/customer/supplier-details?supplierId=${order.supplier.id}`)}
                                whileHover={{
                                  scale: 1.05,
                                  y: -2,
                                  transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
                                }}
                                whileTap={{
                                  scale: 0.95,
                                  transition: { duration: 0.15, ease: "easeInOut" }
                                }}
                                className="bg-gradient-to-r from-purple-600 to-pink-600 text-white py-4 px-6 rounded-2xl font-bold shadow-2xl hover:shadow-purple-500/25 flex items-center justify-center gap-3"
                                style={{
                                  transition: "box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)"
                                }}
                              >
                                <RotateCcw className="w-5 h-5" />
                                Reorder
                              </motion.button>
                            </motion.div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                );
              })}
              </motion.div>
            </AnimatePresence>
          )}
        </motion.div>
        </div>
      </motion.div>
    </div>
  );
};

export default OrdersPage;
