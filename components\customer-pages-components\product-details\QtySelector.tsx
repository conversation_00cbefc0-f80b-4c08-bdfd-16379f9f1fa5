import { <PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Text } from 'tamagui'
import { Ionicons } from '@expo/vector-icons'

type Props = {
  qty: number
  setQty: (n: number) => void
}

export const QtySelector = ({ qty, setQty }: Props) => (
    <XStack ai="center" gap="$2" bg="$gray2" br="$4" p="$4" bw={0.5} boc="$gray6">
      <Button
        circular
        size="$2"
        icon={<Ionicons name="remove" size={12} />}
        onPress={() => setQty(Math.max(1, qty - 1))}
      />
      <Text minWidth={24} ta="center">{qty}</Text>
      <Button
        circular
        size="$2"
        icon={<Ionicons name="add" size={12} />}
        onPress={() => setQty(qty + 1)}
      />
    </XStack>
)