import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from 'react-leaflet';
import L from 'leaflet';
import {
  MapPin,
  Star,
  Clock,
  Phone,
  Eye,
  Navigation,
  Filter,
  Search,
  X,
  Locate,
  ChevronDown,
  Layers,
  Zap
} from 'lucide-react';
import Logo from '../../components/common/Logo';

// Fix for default markers in react-leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Create custom marker with supplier logo
const createCustomMarker = (supplier: typeof mockSuppliers[0]) => {
  const categoryColor = getCategoryColor(supplier.category);
  const statusColor = supplier.isOpen ? '#22c55e' : '#ef4444';

  return L.divIcon({
    html: `
      <div style="
        width: 50px;
        height: 60px;
        position: relative;
      ">
        <!-- Main marker circle -->
        <div style="
          width: 50px;
          height: 50px;
          background: ${categoryColor};
          border: 4px solid white;
          border-radius: 50%;
          box-shadow: 0 2px 10px rgba(0,0,0,0.3);
          position: relative;
        ">
          <!-- Perfectly centered image -->
          <img
            src="${supplier.logoUrl}"
            alt="${supplier.name}"
            style="
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              width: 36px;
              height: 36px;
              object-fit: cover;
              border-radius: 50%;
              background: white;
              border: 2px solid white;
            "
          />
          <!-- Fallback text (hidden by default, shown if image fails) -->
          <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 36px;
            height: 36px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: ${categoryColor};
            font-weight: bold;
            font-size: 18px;
            border: 2px solid white;
            z-index: -1;
          ">
            ${supplier.name.charAt(0).toUpperCase()}
          </div>
        </div>

        <!-- Pointer triangle -->
        <div style="
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          width: 0;
          height: 0;
          border-left: 6px solid transparent;
          border-right: 6px solid transparent;
          border-top: 10px solid ${categoryColor};
          filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
        "></div>

        <!-- Status indicator -->
        <div style="
          position: absolute;
          top: -2px;
          right: -2px;
          width: 16px;
          height: 16px;
          background: ${statusColor};
          border: 3px solid white;
          border-radius: 50%;
          box-shadow: 0 1px 4px rgba(0,0,0,0.3);
        "></div>
      </div>
    `,
    className: 'supplier-marker',
    iconSize: [50, 60],
    iconAnchor: [25, 50],
    popupAnchor: [0, -50]
  });
};

const getCategoryColor = (category: string) => {
  switch (category) {
    case 'restaurant': return '#f97316';
    case 'pharmacy': return '#22c55e';
    case 'electronics': return '#3b82f6';
    case 'grocery': return '#8b5cf6';
    default: return '#6b7280';
  }
};

// Mock suppliers data matching mobile structure
const mockSuppliers = [
  {
    id: '1',
    name: 'Al-Quds Restaurant',
    category: 'restaurant',
    lat: 32.2211,
    lng: 35.2544,
    logoUrl: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=400&h=200&fit=crop',
    rating: 4.8,
    deliveryTime: '25-35 min',
    openHours: '9:00 AM - 11:00 PM',
    phone: '+970599123456',
    tags: ['Fast Food', 'Middle Eastern'],
    isOpen: true
  },
  {
    id: '2',
    name: 'Pizza Palace',
    category: 'restaurant',
    lat: 32.2250,
    lng: 35.2580,
    logoUrl: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=200&fit=crop',
    rating: 4.6,
    deliveryTime: '30-40 min',
    openHours: '11:00 AM - 12:00 AM',
    phone: '+970568987654',
    tags: ['Pizza', 'Italian'],
    isOpen: true
  },
  {
    id: '3',
    name: 'Fresh Pharmacy',
    category: 'pharmacy',
    lat: 32.2180,
    lng: 35.2520,
    logoUrl: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=200&fit=crop',
    rating: 4.9,
    deliveryTime: '15-25 min',
    openHours: '8:00 AM - 10:00 PM',
    phone: '+970597654321',
    tags: ['Medicine', 'Health'],
    isOpen: true
  },
  {
    id: '4',
    name: 'Tech Store',
    category: 'electronics',
    lat: 32.2280,
    lng: 35.2500,
    logoUrl: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=100&h=100&fit=crop&crop=center',
    banner: 'https://images.unsplash.com/photo-**********-b33ff0c44a43?w=400&h=200&fit=crop',
    rating: 4.4,
    deliveryTime: '45-60 min',
    openHours: '9:00 AM - 9:00 PM',
    phone: '+970595956014',
    tags: ['Electronics', 'Gadgets'],
    isOpen: false
  }
];

const categories = [
  { key: 'all', label: 'All Suppliers', color: 'bg-gray-100 text-gray-700' },
  { key: 'restaurant', label: 'Restaurants', color: 'bg-orange-100 text-orange-700' },
  { key: 'pharmacy', label: 'Pharmacies', color: 'bg-green-100 text-green-700' },
  { key: 'electronics', label: 'Electronics', color: 'bg-blue-100 text-blue-700' },
  { key: 'grocery', label: 'Grocery', color: 'bg-purple-100 text-purple-700' }
];

const SuppliersMapPage: React.FC = () => {
  const [selectedSupplier, setSelectedSupplier] = useState<typeof mockSuppliers[0] | null>(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<{lat: number, lng: number} | null>(null);
  const [mapCenter, setMapCenter] = useState<[number, number]>([32.2211, 35.2544]);
  const [isLocating, setIsLocating] = useState(false);
  const [scrollY, setScrollY] = useState(0);
  const [isHeaderCompact, setIsHeaderCompact] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Add marker styles - ATTRACTIVE HOVER WITHOUT MOVEMENT
  useEffect(() => {
    const styleId = 'attractive-marker-styles';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        .supplier-marker {
          cursor: pointer !important;
          transition: filter 0.3s ease !important;
        }

        .supplier-marker:hover {
          filter: brightness(1.1) saturate(1.2) drop-shadow(0 4px 15px rgba(0,0,0,0.4)) !important;
        }

        .supplier-marker:active {
          filter: brightness(0.95) saturate(1.1) drop-shadow(0 2px 8px rgba(0,0,0,0.5)) !important;
        }

        /* Subtle glow effect on hover for the main circle */
        .supplier-marker:hover > div > div:first-child {
          box-shadow:
            0 2px 10px rgba(0,0,0,0.3),
            0 0 20px rgba(255,255,255,0.3),
            inset 0 1px 0 rgba(255,255,255,0.4) !important;
        }

        /* Enhanced status indicator on hover */
        .supplier-marker:hover > div > div:last-child {
          box-shadow:
            0 1px 4px rgba(0,0,0,0.3),
            0 0 8px rgba(255,255,255,0.5) !important;
        }
      `;
      document.head.appendChild(style);
    }
  }, []);

  // Handle scroll for header animation with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);

      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsHeaderCompact(currentScrollY > 200);
      }, 50);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(timeoutId);
    };
  }, []);

  // Filter suppliers based on category and search
  const filteredSuppliers = mockSuppliers.filter(supplier => {
    const matchesCategory = selectedCategory === 'all' || supplier.category === selectedCategory;
    const matchesSearch = searchQuery === '' ||
      supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      supplier.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    return matchesCategory && matchesSearch;
  });

  // Get user's current location
  const getCurrentLocation = async () => {
    setIsLocating(true);
    try {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const location = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            setUserLocation(location);
            setMapCenter([location.lat, location.lng]);
            setIsLocating(false);
          },
          (error) => {
            console.error('Error getting location:', error);
            setIsLocating(false);
          }
        );
      }
    } catch (error) {
      console.error('Geolocation error:', error);
      setIsLocating(false);
    }
  };

  useEffect(() => {
    getCurrentLocation();
  }, []);

  return (
    <>
      <div className="min-h-screen relative overflow-hidden">
        {/* Premium Animated Background */}
        <div className="fixed inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
          {/* Animated gradient orbs */}
          <motion.div
            animate={{
              scale: [1, 1.2, 1],
              opacity: [0.3, 0.6, 0.3],
            }}
            transition={{
              duration: 8,
              repeat: Infinity,
              ease: "easeInOut"
            }}
            className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1.2, 1, 1.2],
              opacity: [0.4, 0.7, 0.4],
            }}
            transition={{
              duration: 10,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 2
            }}
            className="absolute bottom-0 right-0 w-80 h-80 bg-gradient-to-br from-green-500/30 to-blue-600/30 rounded-full blur-3xl"
          />
          <motion.div
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0.5, 0.2],
            }}
            transition={{
              duration: 12,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 4
            }}
            className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-br from-yellow-500/20 to-orange-600/20 rounded-full blur-3xl"
          />

          {/* Floating particles */}
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -30, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3 + Math.random() * 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>

        {/* Main Header Content - Static */}
        <div className="relative z-30 pt-16 pb-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex justify-center mb-6"
              >
                <Logo size="lg" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <h1 className="text-4xl font-bold text-white mb-4">
                  Discover Suppliers
                </h1>
                <p className="text-white/80 text-lg max-w-lg mx-auto">
                  Find the best suppliers near your location
                </p>
              </motion.div>
            </div>
          </div>
        </div>

        {/* Sticky Compact Header - Only appears when scrolling */}
        <motion.div
          className="fixed top-0 left-0 right-0 z-50"
          animate={{
            opacity: isHeaderCompact ? 1 : 0,
            y: isHeaderCompact ? 0 : -100,
            backgroundColor: isHeaderCompact
              ? "rgba(15, 23, 42, 0.95)"
              : "rgba(15, 23, 42, 0)",
            backdropFilter: isHeaderCompact ? "blur(20px)" : "blur(0px)",
            borderBottom: isHeaderCompact
              ? "1px solid rgba(255, 255, 255, 0.1)"
              : "1px solid rgba(255, 255, 255, 0)",
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          style={{ pointerEvents: isHeaderCompact ? 'auto' : 'none' }}
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    className="flex items-center gap-2"
                  >
                    {/* Simplified logo for compact header */}
                    <div className="w-8 h-8 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-lg flex items-center justify-center">
                      <Zap size={16} className="text-white" />
                    </div>
                    <span className="text-white font-bold text-lg">BolTalab</span>
                  </motion.div>
                  <div className="border-l border-white/20 pl-3">
                    <h2 className="text-white font-semibold text-sm">Suppliers Map</h2>
                    <p className="text-white/60 text-xs">Find suppliers near you</p>
                  </div>
                </div>
                <button
                  onClick={getCurrentLocation}
                  disabled={isLocating}
                  className="px-3 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors duration-200 flex items-center gap-2 disabled:opacity-50 text-sm"
                >
                  {isLocating ? (
                    <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  ) : (
                    <Locate size={16} />
                  )}
                  {isLocating ? 'Locating...' : 'My Location'}
                </button>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Search and Filter Panel - Positioned above map */}
        <div className="relative z-40">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-6"
          >
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 p-6">
              {/* Search Bar */}
              <div className="mb-6">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <Search size={20} className="text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search suppliers, categories, or tags..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-12 pr-12 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-lg"
                  />
                  {searchQuery && (
                    <button
                      onClick={() => setSearchQuery('')}
                      className="absolute inset-y-0 right-0 pr-4 flex items-center"
                    >
                      <X size={20} className="text-gray-400 hover:text-gray-600" />
                    </button>
                  )}
                </div>
              </div>

              {/* Enhanced Category Filter with horizontal scroll */}
              <div className="mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                    <Filter size={20} />
                    Categories
                  </h3>
                  <button
                    onClick={getCurrentLocation}
                    disabled={isLocating}
                    className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-xl transition-colors duration-200 flex items-center gap-2 disabled:opacity-50"
                  >
                    {isLocating ? (
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    ) : (
                      <Locate size={18} />
                    )}
                    {isLocating ? 'Locating...' : 'My Location'}
                  </button>
                </div>

                <div className="flex gap-3 overflow-x-auto pb-2 scrollbar-hide">
                  {categories.map((category) => (
                    <motion.button
                      key={category.key}
                      onClick={() => setSelectedCategory(category.key)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className={`flex-shrink-0 px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                        selectedCategory === category.key
                          ? 'bg-primary-600 text-white shadow-lg shadow-primary-600/30'
                          : category.color + ' hover:scale-105 shadow-md'
                      }`}
                    >
                      {category.label}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Results Summary */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>
                  Found {filteredSuppliers.length} supplier{filteredSuppliers.length !== 1 ? 's' : ''}
                  {selectedCategory !== 'all' && ` in ${categories.find(c => c.key === selectedCategory)?.label}`}
                </span>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Live results</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Map Container */}
        <div className="relative z-30 mx-4 sm:mx-6 lg:mx-8 mb-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 overflow-hidden"
            style={{ height: '70vh' }}
          >
            <MapContainer
              center={mapCenter}
              zoom={13}
              style={{ height: '100%', width: '100%' }}
              className="rounded-2xl"
            >
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
              />

              {/* User Location Marker */}
              {userLocation && (
                <Marker position={[userLocation.lat, userLocation.lng]}>
                  <Popup>
                    <div className="text-center">
                      <strong>Your Location</strong>
                    </div>
                  </Popup>
                </Marker>
              )}

              {/* Supplier Markers */}
              {filteredSuppliers.map((supplier) => (
                <Marker
                  key={supplier.id}
                  position={[supplier.lat, supplier.lng]}
                  icon={createCustomMarker(supplier)}
                  eventHandlers={{
                    click: () => setSelectedSupplier(supplier),
                  }}
                >
                  <Popup>
                    <div className="text-center min-w-[200px]">
                      <div className="flex items-center gap-2 mb-2">
                        <img
                          src={supplier.logoUrl}
                          alt={supplier.name}
                          className="w-8 h-8 rounded-full object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                          }}
                        />
                        <div>
                          <h3 className="font-bold text-sm">{supplier.name}</h3>
                          <p className="text-xs text-gray-600 capitalize">{supplier.category}</p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-600 mb-2">
                        <div className="flex items-center gap-1">
                          <Star size={12} className="text-yellow-500 fill-current" />
                          <span>{supplier.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock size={12} />
                          <span>{supplier.deliveryTime}</span>
                        </div>
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        supplier.isOpen
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {supplier.isOpen ? 'Open' : 'Closed'}
                      </div>
                    </div>
                  </Popup>
                </Marker>
              ))}
            </MapContainer>

            {/* Enhanced Legend Overlay */}
            <div className="absolute bottom-6 left-6 bg-white/95 backdrop-blur-xl rounded-xl shadow-lg border border-white/20 p-4 z-[1000]">
              <h4 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                <Zap size={16} className="text-primary-600" />
                Legend
              </h4>
              <div className="space-y-2">
                {categories.slice(1).map((category) => (
                  <div key={category.key} className="flex items-center gap-3">
                    <div
                      className="w-4 h-4 rounded-full border-2 border-white shadow-md"
                      style={{
                        backgroundColor: category.key === 'restaurant' ? '#f97316' :
                                       category.key === 'pharmacy' ? '#22c55e' :
                                       category.key === 'electronics' ? '#3b82f6' :
                                       category.key === 'grocery' ? '#8b5cf6' : '#6b7280'
                      }}
                    ></div>
                    <span className="text-sm text-gray-700 font-medium">{category.label}</span>
                  </div>
                ))}
                <div className="flex items-center gap-3 pt-2 border-t border-gray-200">
                  <div className="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-md"></div>
                  <span className="text-sm text-gray-700 font-medium">Your Location</span>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Enhanced Supplier Details Modal */}
        <AnimatePresence>
          {selectedSupplier && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-md z-[9999] flex items-end md:items-center justify-center p-4"
              onClick={() => setSelectedSupplier(null)}
            >
              <motion.div
                initial={{ y: 100, opacity: 0, scale: 0.9 }}
                animate={{ y: 0, opacity: 1, scale: 1 }}
                exit={{ y: 100, opacity: 0, scale: 0.9 }}
                transition={{ type: "spring", damping: 20, stiffness: 300 }}
                onClick={(e) => e.stopPropagation()}
                className="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/20 max-w-md w-full max-h-[85vh] overflow-hidden flex flex-col"
              >
                {/* Enhanced Supplier Header */}
                <div className="relative flex-shrink-0">
                  <div className="relative h-40 bg-gradient-to-br from-primary-500 to-secondary-500 overflow-hidden">
                    <img
                      src={selectedSupplier.banner}
                      alt={selectedSupplier.name}
                      className="w-full h-full object-cover mix-blend-overlay"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=200&fit=crop';
                      }}
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                  </div>

                  <div className="absolute top-4 right-4">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setSelectedSupplier(null)}
                      className="w-10 h-10 bg-white/90 backdrop-blur-md rounded-full flex items-center justify-center hover:bg-white transition-colors shadow-lg"
                    >
                      <X size={18} className="text-gray-600" />
                    </motion.button>
                  </div>

                  <div className="absolute -bottom-8 left-6">
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                      className="w-16 h-16 bg-white rounded-2xl border-4 border-white shadow-xl overflow-hidden"
                    >
                      <img
                        src={selectedSupplier.logoUrl}
                        alt={selectedSupplier.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          target.parentElement!.innerHTML = `
                            <div class="w-full h-full flex items-center justify-center text-primary-600 font-bold text-lg bg-gradient-to-br from-primary-50 to-secondary-50">
                              ${selectedSupplier.name.charAt(0)}
                            </div>
                          `;
                        }}
                      />
                    </motion.div>
                  </div>
                </div>

                {/* Enhanced Supplier Details */}
                <div className="flex-1 overflow-y-auto p-6 pt-12">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="space-y-6"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-2xl font-bold text-gray-900 mb-2">{selectedSupplier.name}</h3>
                        <p className="text-gray-600 capitalize mb-3 text-lg">{selectedSupplier.category}</p>
                        <div className="flex items-center gap-6 text-sm">
                          <div className="flex items-center gap-2">
                            <Star size={16} className="text-yellow-500 fill-current" />
                            <span className="font-semibold text-gray-900">{selectedSupplier.rating}</span>
                            <span className="text-gray-500">rating</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock size={16} className="text-blue-500" />
                            <span className="font-semibold text-gray-900">{selectedSupplier.deliveryTime}</span>
                          </div>
                        </div>
                      </div>
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.4, type: "spring" }}
                        className={`px-4 py-2 rounded-full text-sm font-semibold shadow-md ${
                          selectedSupplier.isOpen
                            ? 'bg-green-100 text-green-700 border border-green-200'
                            : 'bg-red-100 text-red-700 border border-red-200'
                        }`}
                      >
                        {selectedSupplier.isOpen ? '🟢 Open' : '🔴 Closed'}
                      </motion.div>
                    </div>

                    {/* Enhanced Tags */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">Specialties</h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedSupplier.tags.map((tag, index) => (
                          <motion.span
                            key={tag}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: 0.5 + index * 0.1 }}
                            className="px-3 py-1 bg-gradient-to-r from-primary-100 to-secondary-100 text-primary-700 text-sm rounded-full font-medium border border-primary-200"
                          >
                            {tag}
                          </motion.span>
                        ))}
                      </div>
                    </div>

                    {/* Enhanced Contact Info */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-gray-900">Contact Information</h4>
                      <div className="space-y-3">
                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.6 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Clock size={18} className="text-blue-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Opening Hours</p>
                            <p className="font-semibold text-gray-900">{selectedSupplier.openHours}</p>
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.7 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                            <Phone size={18} className="text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Phone</p>
                            <p className="font-semibold text-gray-900">{selectedSupplier.phone}</p>
                          </div>
                        </motion.div>

                        <motion.div
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.8 }}
                          className="flex items-center gap-4 p-3 bg-gray-50 rounded-xl"
                        >
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <MapPin size={18} className="text-purple-600" />
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Location</p>
                            <p className="font-semibold text-gray-900">Nablus, Palestine</p>
                          </div>
                        </motion.div>
                      </div>
                    </div>

                    {/* Enhanced Action Buttons */}
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.9 }}
                      className="flex gap-3 pt-4"
                    >
                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          window.location.href = `/customer/supplier-details/${selectedSupplier.id}`;
                        }}
                        disabled={!selectedSupplier.isOpen}
                        className="flex-1 px-6 py-4 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-3 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
                      >
                        <Eye size={20} />
                        {selectedSupplier.isOpen ? 'View Menu' : 'Closed'}
                      </motion.button>

                      <motion.button
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => {
                          const url = `https://www.google.com/maps/dir/?api=1&destination=${selectedSupplier.lat},${selectedSupplier.lng}`;
                          window.open(url, '_blank');
                        }}
                        className="px-6 py-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-semibold rounded-xl transition-all duration-200 flex items-center justify-center gap-3 shadow-md"
                      >
                        <Navigation size={20} />
                        Directions
                      </motion.button>
                    </motion.div>
                  </motion.div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </>
  );
};

export default SuppliersMapPage;
