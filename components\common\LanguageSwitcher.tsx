import React from 'react';
import { useTranslation } from 'react-i18next';
import { Button, XStack, Text, Sheet, YStack } from 'tamagui';
import { Ionicons } from '@expo/vector-icons';
import { useLanguageStore, Language } from '../../stores/languageStore';

interface LanguageSwitcherProps {
  variant?: 'button' | 'sheet';
  onLanguageChange?: () => void;
}

export const LanguageSwitcher: React.FC<LanguageSwitcherProps> = ({
  variant = 'button',
  onLanguageChange
}) => {
  const { t } = useTranslation();
  const { currentLanguage, setLanguage, isRTL } = useLanguageStore();
  const [isOpen, setIsOpen] = React.useState(false);

  const handleLanguageChange = async (language: Language) => {
    await setLanguage(language);
    setIsOpen(false);
    onLanguageChange?.();
  };

  const languages = [
    { code: 'en' as Language, name: 'English', nativeName: 'English' },
    { code: 'ar' as Language, name: 'Arabic', nativeName: 'العربية' },
  ];

  if (variant === 'button') {
    return (
      <>
        <Button
          size="$3"
          variant="outlined"
          onPress={() => setIsOpen(true)}
          icon={<Ionicons name="language" size={20} />}
          backgroundColor="$background"
          borderColor="$borderColor"
        >
          <Text fontSize="$3">
            {currentLanguage === 'en' ? 'EN' : 'ع'}
          </Text>
        </Button>

        <Sheet
          modal
          open={isOpen}
          onOpenChange={setIsOpen}
          snapPoints={[30]}
          dismissOnSnapToBottom
        >
          <Sheet.Overlay />
          <Sheet.Handle />
          <Sheet.Frame padding="$4" backgroundColor="$background">
            <YStack space="$3">
              <Text fontSize="$5" fontWeight="600" textAlign="center">
                {t('profile.changeLanguage', { defaultValue: 'Change Language' })}
              </Text>
              
              {languages.map((language) => (
                <Button
                  key={language.code}
                  size="$4"
                  variant={currentLanguage === language.code ? "outlined" : undefined}
                  backgroundColor={currentLanguage === language.code ? "$blue2" : "transparent"}
                  borderColor={currentLanguage === language.code ? "$blue8" : "transparent"}
                  onPress={() => handleLanguageChange(language.code)}
                  justifyContent="space-between"
                >
                  <XStack alignItems="center" space="$3">
                    <Text fontSize="$4" fontWeight="500">
                      {language.nativeName}
                    </Text>
                    <Text fontSize="$3" color="$gray10">
                      ({language.name})
                    </Text>
                  </XStack>
                  {currentLanguage === language.code && (
                    <Ionicons name="checkmark" size={20} color="$blue10" />
                  )}
                </Button>
              ))}
            </YStack>
          </Sheet.Frame>
        </Sheet>
      </>
    );
  }

  // Sheet variant for profile page
  return (
    <YStack space="$3">
      <Text fontSize="$4" fontWeight="600">
        {t('profile.language', { defaultValue: 'Language' })}
      </Text>
      
      {languages.map((language) => (
        <Button
          key={language.code}
          size="$4"
          variant={currentLanguage === language.code ? "outlined" : undefined}
          backgroundColor={currentLanguage === language.code ? "$blue2" : "transparent"}
          borderColor={currentLanguage === language.code ? "$blue8" : "transparent"}
          onPress={() => handleLanguageChange(language.code)}
          justifyContent="space-between"
        >
          <XStack alignItems="center" space="$3">
            <Text fontSize="$4" fontWeight="500">
              {language.nativeName}
            </Text>
            <Text fontSize="$3" color="$gray10">
              ({language.name})
            </Text>
          </XStack>
          {currentLanguage === language.code && (
            <Ionicons name="checkmark" size={20} color="$blue10" />
          )}
        </Button>
      ))}
    </YStack>
  );
};
