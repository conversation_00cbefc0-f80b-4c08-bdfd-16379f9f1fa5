 import AsyncStorage from '@react-native-async-storage/async-storage';
import { getApiBaseUrl } from './networkUtils';

// Get the appropriate API base URL for the current environment
const API_BASE_URL = getApiBaseUrl();

console.log(`🌐 API Base URL: ${API_BASE_URL}`);

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface SignupRequest {
  // Basic Information
  firstName: string;
  lastName: string;
  email: string;

  // Contact & Security
  phoneNumber: string;
  password: string;

  // Profile Setup
  username: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address: string;
  city: string;
  country: string;

  // Account Type
  role: 'customer' | 'supplier';

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;

  // Location & Preferences
  location?: [number, number]; // [longitude, latitude]
  notifications?: boolean;
}

export interface User {
  id: string;
  email: string;
  role: 'customer' | 'supplier' | 'admin';

  // Basic Information
  firstName: string;
  lastName: string;
  username: string;
  phoneNumber: string;

  // Profile Information
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address: string;
  city: string;
  country: string;

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;

  // Location
  location?: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };

  // Preferences
  notifications: boolean;

  // Verification and Security
  isEmailVerified: boolean;
  lastLogin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface UpdateProfileRequest {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  country?: string;
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
}

class ApiService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    // Load tokens from storage on initialization
    this.loadTokensFromStorage();
  }

  private async loadTokensFromStorage() {
    try {
      this.accessToken = await AsyncStorage.getItem('accessToken');
      this.refreshToken = await AsyncStorage.getItem('refreshToken');
    } catch (error) {
      console.error('Error loading tokens from storage:', error);
    }
  }

  private async saveTokensToStorage(accessToken: string, refreshToken: string) {
    try {
      this.accessToken = accessToken;
      this.refreshToken = refreshToken;

      await AsyncStorage.setItem('accessToken', accessToken);
      await AsyncStorage.setItem('refreshToken', refreshToken);
    } catch (error) {
      console.error('Error saving tokens to storage:', error);
    }
  }

  private async clearTokensFromStorage() {
    try {
      this.accessToken = null;
      this.refreshToken = null;

      await AsyncStorage.removeItem('accessToken');
      await AsyncStorage.removeItem('refreshToken');
    } catch (error) {
      console.error('Error clearing tokens from storage:', error);
    }
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${API_BASE_URL}${endpoint}`;

    console.log(`🌐 Making API request to: ${url}`);

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      timeout: 10000, // 10 second timeout
      ...options,
    };

    // Add authorization header if token exists
    if (this.accessToken) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${this.accessToken}`,
      };
    }

    try {
      console.log(`📡 Sending request with config:`, JSON.stringify(config, null, 2));

      const response = await fetch(url, config);
      console.log(`📥 Response status: ${response.status} ${response.statusText}`);

      if (!response.ok) {
        console.error(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`📦 Response data:`, data);

      // If token is expired, try to refresh
      if (response.status === 401 && this.refreshToken) {
        console.log(`🔄 Token expired, attempting refresh...`);
        const refreshed = await this.refreshAccessToken();
        if (refreshed) {
          // Retry the original request with new token
          config.headers = {
            ...config.headers,
            Authorization: `Bearer ${this.accessToken}`,
          };
          const retryResponse = await fetch(url, config);
          return await retryResponse.json();
        }
      }

      return data;
    } catch (error) {
      console.error('❌ API request error:', error);
      console.error('🔍 Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        url,
        config: JSON.stringify(config, null, 2)
      });

      return {
        success: false,
        message: 'Network error occurred. Please check your connection and try again.',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) return false;

    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh-token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refreshToken: this.refreshToken }),
      });

      const data = await response.json();

      if (data.success && data.data) {
        await this.saveTokensToStorage(data.data.accessToken, data.data.refreshToken);
        return true;
      } else {
        await this.clearTokensFromStorage();
        return false;
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      await this.clearTokensFromStorage();
      return false;
    }
  }

  // Authentication methods
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    if (response.success && response.data) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async signup(userData: SignupRequest): Promise<ApiResponse<any>> {
    const response = await this.makeRequest<any>('/auth/signup', {
      method: 'POST',
      body: JSON.stringify(userData),
    });

    // Don't save tokens for signup - user needs to verify email first
    // Tokens will be saved after email verification

    return response;
  }

  async logout(): Promise<ApiResponse> {
    const response = await this.makeRequest('/auth/logout', {
      method: 'POST',
      body: JSON.stringify({ refreshToken: this.refreshToken }),
    });

    await this.clearTokensFromStorage();
    return response;
  }

  async forgotPassword(email: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    });
  }

  async verifyResetCode(email: string, code: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/verify-reset-code', {
      method: 'POST',
      body: JSON.stringify({ email, code }),
    });
  }

  async resetPassword(email: string, code: string, password: string): Promise<ApiResponse> {
    return this.makeRequest('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({ email, code, password }),
    });
  }

  // User methods
  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    return this.makeRequest<{ user: User }>('/users/profile');
  }

  async updateProfile(updates: UpdateProfileRequest): Promise<ApiResponse<{ user: User }>> {
    return this.makeRequest<{ user: User }>('/users/profile', {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<ApiResponse> {
    return this.makeRequest('/users/change-password', {
      method: 'PUT',
      body: JSON.stringify({ currentPassword, newPassword }),
    });
  }

  async verifyEmail(data: { token: string }): Promise<ApiResponse<AuthResponse>> {
    const response = await this.makeRequest<AuthResponse>('/auth/verify-email', {
      method: 'POST',
      body: JSON.stringify(data),
    });

    if (response.success && response.data) {
      await this.saveTokensToStorage(response.data.accessToken, response.data.refreshToken);
    }

    return response;
  }

  async resendVerificationEmail(data: { email: string }): Promise<ApiResponse<void>> {
    return await this.makeRequest<void>('/auth/resend-verification', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Utility methods
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  getAccessToken(): string | null {
    return this.accessToken;
  }
}

export const apiService = new ApiService();
 


