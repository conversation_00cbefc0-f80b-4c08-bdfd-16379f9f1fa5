// Simple test script to verify API connectivity
const fetch = require('node-fetch');

const API_BASE_URL = 'http://***********:3000/api';

async function testAPI() {
  console.log('🧪 Testing API connectivity...\n');

  // Test 1: Health check
  try {
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch('http://***********:3000/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.message);
  } catch (error) {
    console.log('❌ Health check failed:', error.message);
    return;
  }

  // Test 2: API documentation endpoint
  try {
    console.log('\n2. Testing API documentation endpoint...');
    const apiResponse = await fetch(`${API_BASE_URL}`);
    const apiData = await apiResponse.json();
    console.log('✅ API docs accessible:', apiData.message);
  } catch (error) {
    console.log('❌ API docs failed:', error.message);
  }

  // Test 3: Login endpoint (should fail with validation error)
  try {
    console.log('\n3. Testing login endpoint...');
    const loginResponse = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'testpassword'
      })
    });
    
    const loginData = await loginResponse.json();
    console.log('✅ Login endpoint accessible');
    console.log('Response:', loginData.message);
  } catch (error) {
    console.log('❌ Login endpoint failed:', error.message);
  }

  console.log('\n🎉 API connectivity test completed!');
}

testAPI();
