import mongoose, { Document, Schema } from 'mongoose';

export interface IService extends Document {
  key: string;
  label: string;
  icon: string;
  color: string;
  route: string;
  isActive: boolean;
  order: number;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

const ServiceSchema: Schema = new Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  label: {
    type: String,
    required: true,
    trim: true
  },
  icon: {
    type: String,
    required: true,
    trim: true
  },
  color: {
    type: String,
    required: true,
    trim: true
  },
  route: {
    type: String,
    required: true,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  order: {
    type: Number,
    default: 0
  },
  description: {
    type: String,
    trim: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
ServiceSchema.index({ isActive: 1, order: 1 });

export const Service = mongoose.model<IService>('Service', ServiceSchema);
