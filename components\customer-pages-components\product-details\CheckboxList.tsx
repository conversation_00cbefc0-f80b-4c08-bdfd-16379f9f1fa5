import { Checkbox, Label, XStack, YStack } from 'tamagui'

type Props = {
  items: string[]
  selected: string[]
  onToggle: (id: string) => void
}

export const CheckboxList = ({ items, selected, onToggle }: Props) => (
  <YStack gap="$2">
    {items.map((item) => (
      <XStack key={item} gap="$3" ai="center">
        <Checkbox
          checked={selected.includes(item)}
          onCheckedChange={() => onToggle(item)}
        />
        <Label>{item}</Label>
      </XStack>
    ))}
  </YStack>
)
