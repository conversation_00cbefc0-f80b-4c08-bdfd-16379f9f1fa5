import { create } from 'zustand';
import { CartItem } from '../CartStore';

type Location = {
  lat: number;
  lng: number;
  address?: string;
};

export type MyOrder = {
  id: string
  createdAt: string
  items: CartItem[]
  supplierId: string
  total: number
  status: 'Pending' | 'Preparing' | 'On the Way' | 'Delivered'
  supplierRecievedMoney: boolean
  address: Location | null
  phone: string
  note: string
  paymentMethod: 'cash' | 'card'
  promo: string
  subTotal: number
  deliveryFee: number
  estimatedTime: string
  driverName: string
  driverPhone: string
  driverLocation: Location
};

type Store = {
  orders: MyOrder[]
  addOrder: (order: MyOrder) => void
};

// 🎭 SILLY TEST DATA - REMOVE AFTER TESTING
const generateSillyTestOrders = (): MyOrder[] => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  return [
    {
      id: 'ORD-001',
      createdAt: new Date(today.getTime() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      supplierId: '3a-kefak',
      status: 'Pending',
      total: 45.50,
      subTotal: 40.50,
      deliveryFee: 5.00,
      supplierRecievedMoney: false,
      paymentMethod: 'cash',
      phone: '+970591234567',
      note: 'Extra spicy please! 🌶️',
      promo: '',
      estimatedTime: '25-35 mins',
      driverName: 'Ahmad الطيار',
      driverPhone: '+970599876543',
      address: {
        lat: 32.2211,
        lng: 35.2544,
        address: 'شارع فيصل، وسط البلد، نابلس'
      },
      driverLocation: {
        lat: 32.2200,
        lng: 35.2530,
        address: 'Near Al-Najah University'
      },
      items: [
        {
          id: 1,
          product: {
            id: 'shawerma-roll-1',
            name: 'لفة شاورما لحم',
            price: 12.00,
            image: 'https://example.com/shawarma.jpg',
            category: 'Shawarma',
            restaurantOptions: {
              additions: [
                { id: 'extra-meat', name: 'زيادة لحم', price: 3 }
              ],
              without: ['طحينية'],
              sides: [
                { id: 'pickles', name: 'مخللات', price: 1 }
              ]
            }
          },
          qty: 2,
          finalPrice: 27.00, // (12 + 3 + 1) * 2 = 32, but with some discount
          supplierId: '3a-kefak',
          supplierName: '3a Kefak',
          supplierCategory: 'Restaurant',
          selectedAdditions: [
            { id: 'extra-meat', name: 'زيادة لحم', price: 3 }
          ],
          selectedSides: [
            { id: 'pickles', name: 'مخللات', price: 1 }
          ],
          without: ['طحينية']
        },
        {
          id: 2,
          product: {
            id: 'drink-1',
            name: 'كوكا كولا',
            price: 3.50,
            image: 'https://example.com/coke.jpg',
            category: 'Drinks'
          },
          qty: 2,
          finalPrice: 7.00, // 3.50 * 2
          supplierId: '3a-kefak',
          supplierName: '3a Kefak',
          supplierCategory: 'Restaurant'
        }
      ]
    },
    {
      id: 'ORD-002',
      createdAt: new Date(today.getTime() + 1 * 60 * 60 * 1000).toISOString(), // 1 hour ago
      supplierId: '3a-kefak',
      status: 'Preparing',
      total: 28.00,
      subTotal: 23.00,
      deliveryFee: 5.00,
      supplierRecievedMoney: false,
      paymentMethod: 'card',
      phone: '+970592345678',
      note: 'No onions please 🧅❌',
      promo: 'SAVE10',
      estimatedTime: '15-20 mins',
      driverName: 'محمد السريع',
      driverPhone: '+970598765432',
      address: {
        lat: 32.2250,
        lng: 35.2600,
        address: 'حي الشرق، نابلس'
      },
      driverLocation: {
        lat: 32.2240,
        lng: 35.2580,
        address: 'On the way to customer'
      },
      items: [
        {
          id: 3,
          product: {
            id: 'shawerma-meal-1',
            name: 'وجبة شاورما عربي لحم',
            price: 15.00,
            image: 'https://example.com/meal.jpg',
            category: 'Meals',
            restaurantOptions: {
              additions: [],
              without: ['بصل'],
              sides: [
                { id: 'drink', name: 'مشروب غازي', price: 3 }
              ]
            }
          },
          qty: 1,
          finalPrice: 18.00, // 15 + 3 for drink
          supplierId: '3a-kefak',
          supplierName: '3a Kefak',
          supplierCategory: 'Restaurant',
          selectedSides: [
            { id: 'drink', name: 'مشروب غازي', price: 3 }
          ],
          without: ['بصل']
        }
      ]
    },
    {
      id: 'ORD-003',
      createdAt: new Date(today.getTime() + 30 * 60 * 1000).toISOString(), // 30 mins ago
      supplierId: 'Heart-Attack',
      status: 'On the Way',
      total: 67.00,
      subTotal: 62.00,
      deliveryFee: 5.00,
      supplierRecievedMoney: true,
      paymentMethod: 'card',
      phone: '+970593456789',
      note: 'Ring the bell twice! 🔔🔔',
      promo: '',
      estimatedTime: '5-10 mins',
      driverName: 'يوسف البرق',
      driverPhone: '+970597654321',
      address: {
        lat: 32.2180,
        lng: 35.2480,
        address: 'شارع عمر المختار، نابلس'
      },
      driverLocation: {
        lat: 32.2190,
        lng: 35.2490,
        address: 'Almost there!'
      },
      items: [
        {
          id: 4,
          product: {
            id: 'burger-1',
            name: 'Chili Burger',
            price: 32.00,
            image: 'https://example.com/burger.jpg',
            category: 'Burgers',
            restaurantOptions: {
              additions: [
                { id: 'extra-meat', name: 'Extra Meat', price: 5 }
              ],
              without: [],
              sides: [
                { id: 'fries', name: 'بطاطا مقلية', price: 5 },
                { id: 'drink', name: 'مشروب غازي', price: 3 }
              ]
            }
          },
          qty: 1,
          finalPrice: 45.00, // 32 + 5 + 5 + 3
          supplierId: 'Heart-Attack',
          supplierName: 'Heart Attack',
          supplierCategory: 'Restaurant',
          selectedAdditions: [
            { id: 'extra-meat', name: 'Extra Meat', price: 5 }
          ],
          selectedSides: [
            { id: 'fries', name: 'بطاطا مقلية', price: 5 },
            { id: 'drink', name: 'مشروب غازي', price: 3 }
          ]
        },
        {
          id: 5,
          product: {
            id: 'burger-2',
            name: 'Cheeseburger Delux',
            price: 30.00,
            image: 'https://example.com/cheeseburger.jpg',
            category: 'Burgers'
          },
          qty: 1,
          finalPrice: 20.00, // discounted from 30
          supplierId: 'Heart-Attack',
          supplierName: 'Heart Attack',
          supplierCategory: 'Restaurant'
        }
      ]
    },
    {
      id: 'ORD-004',
      createdAt: new Date(today.getTime() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago (delivered)
      supplierId: '3a-kefak',
      status: 'Delivered',
      total: 35.50,
      subTotal: 30.50,
      deliveryFee: 5.00,
      supplierRecievedMoney: true,
      paymentMethod: 'cash',
      phone: '+970594567890',
      note: 'Thank you! 😊',
      promo: '',
      estimatedTime: 'Delivered',
      driverName: 'سامر الأمين',
      driverPhone: '+970596543210',
      address: {
        lat: 32.2300,
        lng: 35.2650,
        address: 'حي الجنوب، نابلس'
      },
      driverLocation: {
        lat: 32.2300,
        lng: 35.2650,
        address: 'Delivered successfully'
      },
      items: [
        {
          id: 6,
          product: {
            id: 'shawerma-roll-1',
            name: 'لفة شاورما لحم',
            price: 12.00,
            image: 'https://example.com/shawarma.jpg',
            category: 'Shawarma'
          },
          qty: 2,
          finalPrice: 24.00, // 12 * 2
          supplierId: '3a-kefak',
          supplierName: '3a Kefak',
          supplierCategory: 'Restaurant'
        },
        {
          id: 7,
          product: {
            id: 'juice-1',
            name: 'عصير برتقال طبيعي',
            price: 6.50,
            image: 'https://example.com/juice.jpg',
            category: 'Juices'
          },
          qty: 1,
          finalPrice: 6.50,
          supplierId: '3a-kefak',
          supplierName: '3a Kefak',
          supplierCategory: 'Restaurant'
        }
      ]
    },
    {
      id: 'ORD-005',
      createdAt: new Date(today.getTime() + 10 * 60 * 1000).toISOString(), // 10 mins ago
      supplierId: 'lc-waikiki',
      status: 'Pending',
      total: 340.00,
      subTotal: 335.00,
      deliveryFee: 5.00,
      supplierRecievedMoney: false,
      paymentMethod: 'card',
      phone: '+970595678901',
      note: 'Size L please, and black color 🖤',
      promo: 'FASHION20',
      estimatedTime: '45-60 mins',
      driverName: 'علي الموضة',
      driverPhone: '+970595432109',
      address: {
        lat: 32.2350,
        lng: 35.2700,
        address: 'شارع الملوك، نابلس'
      },
      driverLocation: {
        lat: 32.2340,
        lng: 35.2680,
        address: 'Preparing for pickup'
      },
      items: [
        {
          id: 8,
          product: {
            id: 'boot-1',
            name: 'Nike Boots',
            price: 120.00,
            image: 'https://example.com/boots.jpg',
            category: 'Shoes',
            clothingOptions: {
              sizes: ['L', 'XL', 'XXL'],
              colors: ['Black', 'Brown', 'White'],
              gallery: ['https://example.com/boots1.jpg', 'https://example.com/boots2.jpg']
            }
          },
          qty: 1,
          finalPrice: 120.00,
          supplierId: 'lc-waikiki',
          supplierName: 'LC Waikiki',
          supplierCategory: 'Fashion',
          selectedSize: 'L',
          selectedColor: 'Black'
        },
        {
          id: 9,
          product: {
            id: 'jacket-1',
            name: 'Turkesh Jacket',
            price: 200.00,
            image: 'https://example.com/jacket.jpg',
            category: 'Jackets',
            clothingOptions: {
              sizes: ['M', 'L', 'XL'],
              colors: ['Black', 'Navy', 'Gray'],
              gallery: ['https://example.com/jacket1.jpg', 'https://example.com/jacket2.jpg']
            }
          },
          qty: 1,
          finalPrice: 180.00, // discounted from 200
          supplierId: 'lc-waikiki',
          supplierName: 'LC Waikiki',
          supplierCategory: 'Fashion',
          selectedSize: 'L',
          selectedColor: 'Black'
        }
      ]
    },
    {
      id: 'ORD-006',
      createdAt: new Date(today.getTime() + 45 * 60 * 1000).toISOString(), // 45 mins ago
      supplierId: 'zemrcado',
      status: 'Preparing',
      total: 31.00,
      subTotal: 26.00,
      deliveryFee: 5.00,
      supplierRecievedMoney: false,
      paymentMethod: 'cash',
      phone: '+970596789012',
      note: 'Fresh fish please! 🐟',
      promo: '',
      estimatedTime: '20-30 mins',
      driverName: 'خالد البحار',
      driverPhone: '+970594321098',
      address: {
        lat: 32.2120,
        lng: 35.2400,
        address: 'شارع البحر، نابلس'
      },
      driverLocation: {
        lat: 32.2130,
        lng: 35.2420,
        address: 'Getting fresh catch'
      },
      items: [
        {
          id: 10,
          product: {
            id: 'fish-fillet-1',
            name: 'Zemrcado Fish Fillet',
            price: 20.00,
            image: 'https://example.com/fish.jpg',
            category: 'Sea Food'
          },
          qty: 1,
          finalPrice: 20.00,
          supplierId: 'zemrcado',
          supplierName: 'Zemrcado',
          supplierCategory: 'Restaurant'
        },
        {
          id: 11,
          product: {
            id: 'juice-1',
            name: 'Aloevera Watermellon Juice',
            price: 5.00,
            image: 'https://example.com/aloe-juice.jpg',
            category: 'Juices'
          },
          qty: 1,
          finalPrice: 5.00,
          supplierId: 'zemrcado',
          supplierName: 'Zemrcado',
          supplierCategory: 'Restaurant'
        }
      ]
    }
  ];
};

export const useMyOrdersStore = create<Store>((set) => ({
  orders: generateSillyTestOrders(), // 🎭 TEMPORARY TEST DATA
  addOrder: (order) => set((state) => ({
    orders: [order, ...state.orders] // latest first
  }))
}));
