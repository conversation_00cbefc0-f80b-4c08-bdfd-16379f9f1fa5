import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { apiService } from '../services/api';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'customer' | 'supplier';
  username: string;
  phoneNumber: string;
  isEmailVerified: boolean;

  // Profile Information
  dateOfBirth?: string;
  gender?: 'male' | 'female' | 'other';

  // Address Information
  address: string;
  city: string;
  country: string;

  // Business Information (for suppliers)
  supplierId?: string;
  storeName?: string;
  businessType?: 'restaurant' | 'clothing' | 'grocery' | 'pharmacy' | 'electronics' | 'other';
  openHours?: string;

  // Location
  location?: {
    type: 'Point';
    coordinates: [number, number]; // [longitude, latitude]
  };

  // Preferences
  notifications: boolean;
  lastLogin?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  setUser: (user: User | null) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already authenticated on app start
    const checkAuthStatus = async () => {
      try {
        if (apiService.isAuthenticated()) {
          // You might want to validate the token with the server here
          // For now, we'll assume the token is valid if it exists
          const token = apiService.getAccessToken();
          if (token) {
            // You could decode the JWT token to get user info
            // or make an API call to get current user data
            // For now, we'll set a realistic placeholder with full user data
            setUser({
              id: 'customer_001',
              email: '<EMAIL>',
              firstName: 'Omar',
              lastName: 'Hassan',
              role: 'customer',
              username: 'omar_hassan',
              phoneNumber: '+962 79 123 4567',
              isEmailVerified: true,
              dateOfBirth: '1995-03-15',
              gender: 'male',
              address: 'Al-Abdali Boulevard, Building 15, Apt 302',
              city: 'Amman',
              country: 'Jordan',
              notifications: true,
              lastLogin: new Date().toISOString(),
              isActive: true,
              createdAt: '2023-01-15T10:30:00Z',
              updatedAt: new Date().toISOString(),
            });
          }
        }
      } catch (error) {
        console.error('Auth check failed:', error);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await apiService.login({ email, password });
      
      if (response.success && response.data) {
        setUser(response.data.user);
        return { success: true };
      } else {
        return { 
          success: false, 
          message: response.message || 'Login failed' 
        };
      }
    } catch (error) {
      return { 
        success: false, 
        message: 'An unexpected error occurred' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
    }
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    logout,
    setUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
