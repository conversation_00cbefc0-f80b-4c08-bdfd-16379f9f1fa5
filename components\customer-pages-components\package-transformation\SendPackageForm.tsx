import { ScrollView } from 'react-native';
import { Button, Input, Label, Text, View, YStack, Card, Separator, XStack } from 'tamagui';
import { useRouter } from 'expo-router';
import { MotiView } from 'moti';
import { Ionicons } from '@expo/vector-icons';
import { useSendPackageStore, useSetSendPackage } from './useSendPackageStore';

export function SendPackageForm() {
  const router = useRouter();
  const {
    pickup,
    dropoff,
    receiverName,
    receiverPhone,
    packageType,
    notes,
    updateField,
    setPickup,
    setDropoff,
    reset
  } = useSetSendPackage();
  const { addSendRequest } = useSendPackageStore();

  const isFormValid = () =>
    pickup && dropoff && receiverName.trim() && receiverPhone.trim() && packageType.trim();

  return (
    <>
      {/* Header */}
      <View
        style={{
          width: '100%',
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomLeftRadius: 32,
          borderBottomRightRadius: 32,
          backgroundImage: 'linear-gradient(90deg, #7529B3, #8F3DD2)',
          backgroundColor: '#7529B3',
        }}
      >
        <MotiView from={{ opacity: 0, translateY: -10 }} animate={{ opacity: 1, translateY: 0 }}>
          <Text fontSize="$8" fontWeight="700" color="white" textAlign="center">
            🚚 Send a Package
          </Text>
        </MotiView>
      </View>

      <ScrollView contentContainerStyle={{ flexGrow: 1, paddingBottom: 40 }}>
        <YStack gap="$4" p="$4">
          {/* Locations Section */}
          <Card padded elevation="$2" bordered>
            <Text fontWeight="600" fontSize="$6" mb="$2">
              📍 Locations
            </Text>

            <YStack gap="$3">
              <Label>Pickup Location <Text color="red">*</Text></Label>
              <XStack gap="$2">
                <Input
                  flex={1}
                  value={pickup?.address}
                  placeholder="Set on map"
                  editable={false}
                  onChangeText={(text) => setPickup({lat: 32.00000, lng: 32.00000,address: text})} //temporarly
                  size="$4"
                  borderRadius="$4"
                />
                <Button
                  size="$4"
                  onPress={() => router.push('/home/<USER>')}
                >
                  Set
                </Button>
              </XStack>

              <Label>Drop-off Location <Text color="red">*</Text></Label>
              <XStack gap="$2">
                <Input
                  flex={1}
                  value={dropoff?.address}
                  placeholder="Set on map"
                  editable={false}
                  onChangeText={(text) => setDropoff({lat: 32.00000, lng: 32.00000,address: text})} //temporarly
                  size="$4"
                  borderRadius="$4"
                />
                <Button
                  size="$4"
                  onPress={() => router.push('/home/<USER>')}
                >
                  Set
                </Button>
              </XStack>
            </YStack>
          </Card>

          {/* Receiver Info */}
          <Card padded elevation="$2" bordered>
            <Text fontWeight="600" fontSize="$6" mb="$2">
              👤 Receiver Info
            </Text>

            <YStack gap="$3">
              <Label>Full Name <Text color="red">*</Text></Label>
              <Input
                placeholder="e.g. Ahmad Jaber"
                value={receiverName}
                onChangeText={(text) => updateField('receiverName', text)}
                borderRadius="$4"
                size="$4"
              />

              <Label>Phone Number <Text color="red">*</Text></Label>
              <Input
                placeholder="e.g. 0599123456"
                keyboardType="phone-pad"
                value={receiverPhone}
                onChangeText={(text) => updateField('receiverPhone', text)}
                borderRadius="$4"
                size="$4"
              />
            </YStack>
          </Card>

          {/* Package Info */}
          <Card padded elevation="$2" bordered>
            <Text fontWeight="600" fontSize="$6" mb="$2">
              📦 Package Details
            </Text>

            <YStack gap="$3">
              <Label>Type <Text color="red">*</Text></Label>
              <Input
                placeholder="e.g. Fragile, Electronics"
                value={packageType}
                onChangeText={(text) => updateField('packageType', text)}
                borderRadius="$4"
                size="$4"
              />

              <Label>Notes (optional)</Label>
              <Input
                placeholder="Optional instructions for the driver"
                value={notes}
                onChangeText={(text) => updateField('notes', text)}
                borderRadius="$4"
                multiline
                numberOfLines={3}
                size="$4"
              />
            </YStack>
          </Card>

          <Separator />

          {/* Confirm Button */}
          <Button
            width={"100%"}
            height={"$5"}
            size="$9"
            bg="$primary"
            color="white"
            br="$10"
            fontSize={"$6"}
            icon={<Ionicons name="mail-open-outline" size={25} color="white" />}
            disabled={!isFormValid()}
            opacity={isFormValid() ? 1 : 0.5}
            onPress={() => {
              addSendRequest({
                pickup,
                dropoff,
                receiverName,
                receiverPhone,
                packageType,
                notes,
                driverName: 'Ali Alaa', // temporarly
                driverPhone: '0595959595',
                status: 'on the way',
              });
              reset();
              router.push('/home/<USER>');
            }}
            pressStyle={{ bg: "$third" }}
          >
              Confirm Request
          </Button>
        </YStack>
      </ScrollView>
    </>
  );
}