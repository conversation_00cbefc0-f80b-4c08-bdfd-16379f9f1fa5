import React, { useState } from 'react';
import { YStack, Text, H5, XStack } from 'tamagui';
import { useSignupStore } from '../useSignupStore';
import { CustomTextField } from '../../CustomTextField';
import { Ionicons } from '@expo/vector-icons';
import { useTranslation } from 'react-i18next';

export const ContactSecurityStep = () => {
  const { t } = useTranslation();
  const { signupData, updateSignupData } = useSignupStore();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const passwordsMatch = signupData.password === signupData.confirmPassword;
  const passwordValid = signupData.password.length >= 6;

  return (
    <YStack gap="$4" paddingTop="$2">
      <YStack gap="$2" alignItems="center" marginBottom="$4">
        <Ionicons name="shield-checkmark" size={48} color="#7529B3" />
        <H5 textAlign="center" color="$primary">{t('signup.contactSecurity.title', { defaultValue: 'Contact & Security' })}</H5>
        <Text textAlign="center" color="$gray10" fontSize="$3">
          {t('signup.contactSecurity.subtitle', { defaultValue: 'Add your contact details and create a secure password' })}
        </Text>
      </YStack>

      <YStack gap="$4">
        <CustomTextField
          label={t('signup.contactSecurity.phoneNumber', { defaultValue: 'Phone Number' })}
          placeholder={t('signup.contactSecurity.enterPhone', { defaultValue: 'Enter your phone number' })}
          value={signupData.phone}
          onChangeText={(text) => updateSignupData({ phone: text })}
          icon="call"
          keyboardType="phone-pad"
          required
        />

        <YStack gap="$2">
          <CustomTextField
            label={t('auth.password', { defaultValue: 'Password' })}
            placeholder={t('signup.contactSecurity.createPassword', { defaultValue: 'Create a password (min 6 characters)' })}
            value={signupData.password}
            onChangeText={(text) => updateSignupData({ password: text })}
            icon="lock-closed"
            secureTextEntry={!showPassword}
            required
            rightIcon={
              <Ionicons
                name={showPassword ? "eye-off" : "eye"}
                size={20}
                color="#666"
                onPress={() => setShowPassword(!showPassword)}
              />
            }
          />
          {signupData.password && !passwordValid && (
            <Text fontSize="$2" color="$red9">
              {t('signup.contactSecurity.passwordMinLength', { defaultValue: 'Password must be at least 6 characters long' })}
            </Text>
          )}
        </YStack>

        <YStack gap="$2">
          <CustomTextField
            label={t('auth.confirmPassword', { defaultValue: 'Confirm Password' })}
            placeholder={t('signup.contactSecurity.confirmPassword', { defaultValue: 'Confirm your password' })}
            value={signupData.confirmPassword}
            onChangeText={(text) => updateSignupData({ confirmPassword: text })}
            icon="lock-closed"
            secureTextEntry={!showConfirmPassword}
            required
            rightIcon={
              <Ionicons
                name={showConfirmPassword ? "eye-off" : "eye"}
                size={20}
                color="#666"
                onPress={() => setShowConfirmPassword(!showConfirmPassword)}
              />
            }
          />
          {signupData.confirmPassword && !passwordsMatch && (
            <Text fontSize="$2" color="$red9">
              {t('signup.contactSecurity.passwordsDoNotMatch', { defaultValue: 'Passwords do not match' })}
            </Text>
          )}
          {signupData.confirmPassword && passwordsMatch && passwordValid && (
            <XStack alignItems="center" gap="$2">
              <Ionicons name="checkmark-circle" size={16} color="#10b981" />
              <Text fontSize="$2" color="$green9">
                Passwords match
              </Text>
            </XStack>
          )}
        </YStack>
      </YStack>

      <YStack gap="$2" marginTop="$3">
        <Text fontSize="$2" color="$gray9" textAlign="center">
          Your password should be secure and memorable
        </Text>
      </YStack>
    </YStack>
  );
};
