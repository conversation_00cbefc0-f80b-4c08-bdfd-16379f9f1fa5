import React from 'react'
import { TouchableOpacity } from 'react-native'
import { XStack } from 'tamagui'

type Props = {
  colors: string[]
  selected: string | undefined
  onSelect: (c: string) => void
}

export const ColorSwatchRow = ({ colors, selected, onSelect }: Props) => (
  <XStack gap="$3" fw="wrap">
    {colors.map((hex) => (
      <TouchableOpacity key={hex} onPress={() => onSelect(hex)}>
        <XStack
          width={32}
          height={32}
          borderRadius={16}
          ai="center"
          jc="center"
          style={{
            backgroundColor: hex,
            borderWidth: selected === hex ? 2 : 0,
            borderColor: selected === hex ? '#7529B3' : 'transparent',
          }}
        />
      </TouchableOpacity>
    ))}
  </XStack>
)
