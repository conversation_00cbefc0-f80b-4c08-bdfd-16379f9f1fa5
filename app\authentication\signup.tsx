import { Stack/*, useLocalSearchParams*/ } from 'expo-router';
import { ScreenContent } from '~/components/authentication-components/ScreenContent';

export default function Signup() {
  //const { user-name } = useLocalSearchParams();

  return (
    <>
      <Stack.Screen options={{ title: 'Signup', headerShown: false }} />
      <ScreenContent path="app/authentication/signup.tsx" title={`Signup`} />
    </>
  );
}
