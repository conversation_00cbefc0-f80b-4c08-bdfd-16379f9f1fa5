import { Checkbox, Label, XStack, YStack, Text } from 'tamagui';
import { Chip } from './Chip';

type Addition = { id: string; name: string; price: number }

type Props = {
  items: Addition[]
  selected: string[]
  onToggle: (id: string) => void
}

export const AdditionsChecklist = ({ items, selected, onToggle }: Props) => (
  <XStack flexWrap="wrap" gap="$2">
    {items.map((item) => (
      <Chip
        key={item.id}
        label={item.name}
        selected={selected.includes(item.id)}
        onPress={() => onToggle(item.id)}
        iconAfter={`+₪${item.price}`}
      />
    ))}
  </XStack>
)
