import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Button, Alert } from 'react-native';
import { apiService } from '../../services/api';

export const ApiTestComponent: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const testApiConnection = async () => {
    setIsLoading(true);
    setTestResults([]);

    // Common IP addresses to test
    const ipsToTest = [
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '***********',
      '********',
      '********'
    ];

    try {
      addResult('🧪 Starting comprehensive API connectivity test...');
      addResult('🔍 Testing multiple IP addresses...');

      for (const ip of ipsToTest) {
        addResult(`📡 Testing ${ip}:3000...`);

        try {
          const testUrl = `http://${ip}:3000/health`;

          // Create AbortController for timeout functionality
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 3000);

          const response = await fetch(testUrl, {
            method: 'GET',
            signal: controller.signal
          });

          // Clear timeout if request completes successfully
          clearTimeout(timeoutId);

          if (response.ok) {
            const data = await response.json();
            addResult(`✅ SUCCESS! Found server at ${ip}:3000`);
            addResult(`📝 Response: ${data.message}`);
            addResult(`🎯 Update your API config to use: ${ip}`);
            break;
          }
        } catch (error) {
          addResult(`❌ ${ip}:3000 - Not reachable`);
        }
      }

      addResult('');
      addResult('📋 Manual IP Check Instructions:');
      addResult('1. Open Command Prompt (cmd)');
      addResult('2. Type: ipconfig');
      addResult('3. Look for "IPv4 Address"');
      addResult('4. Update DEVELOPMENT_IP in services/api.ts');

    } catch (error) {
      addResult(`❌ Test failed: ${error}`);
      console.error('API Test Error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <View style={{ padding: 20, backgroundColor: '#f5f5f5', margin: 10, borderRadius: 8 }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 10 }}>
        🔧 API Connection Test
      </Text>
      
      <View style={{ flexDirection: 'row', gap: 10, marginBottom: 15 }}>
        <Button 
          title={isLoading ? "Testing..." : "Test API"} 
          onPress={testApiConnection}
          disabled={isLoading}
        />
        <Button 
          title="Clear" 
          onPress={clearResults}
          color="#666"
        />
      </View>
      
      <View style={{ backgroundColor: '#000', padding: 10, borderRadius: 4, minHeight: 100 }}>
        {testResults.length === 0 ? (
          <Text style={{ color: '#888', fontStyle: 'italic' }}>
            Press "Test API" to check connection...
          </Text>
        ) : (
          testResults.map((result, index) => (
            <Text key={index} style={{ color: '#fff', fontSize: 12, marginBottom: 2 }}>
              {result}
            </Text>
          ))
        )}
      </View>
      
      <Text style={{ fontSize: 12, color: '#666', marginTop: 10 }}>
        This test checks if your app can connect to the backend API.
        {'\n'}Expected: API reachable with "Invalid credentials" message.
      </Text>
    </View>
  );
};
