import crypto from 'crypto';

export class CryptoUtils {
  /**
   * Generate a random token for email verification or password reset
   */
  static generateRandomToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash a token for secure storage
   */
  static hashToken(token: string): string {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Generate a secure random string
   */
  static generateSecureRandom(length: number = 16): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    const randomArray = crypto.randomBytes(length);
    
    for (let i = 0; i < length; i++) {
      result += chars[randomArray[i] % chars.length];
    }
    
    return result;
  }

  /**
   * Generate a numeric OTP
   */
  static generateOTP(length: number = 6): string {
    const digits = '0123456789';
    let otp = '';
    const randomArray = crypto.randomBytes(length);
    
    for (let i = 0; i < length; i++) {
      otp += digits[randomArray[i] % digits.length];
    }
    
    return otp;
  }
}
